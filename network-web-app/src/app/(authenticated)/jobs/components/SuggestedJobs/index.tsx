'use client';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import Link from 'next/link';
import { SearchInput, Tabs, FilterModal } from '@/components';
import useAuth from '@/hooks/useAuth';
import Filter from '@assets/svg/Filter';
import {
  fetchJobsForApplicantAPI,
  fetchJobsForCandidateAPI,
} from '@/networks/jobs';
import type {
  FetchJobsForApplicantQueryI,
  FetchJobsForApplicantResultItemI,
  FetchJobsQueryI,
  FetchJobsResultItemI,
  ApplicantsStatusI,
} from '@/networks/jobs/types';
import {
  SuggestedJobItemI,
  SuggestedJobsPropsI,
  SuggestedJobsTabI,
} from './types';
import useJobFilters from './useHook';
import { formatDate } from '@/utils/format-date';
import JobItemSkeleton from '../JobsSkelton';

const tabs: SuggestedJobsTabI[] = [
  { id: 'SUGGUESTED', label: 'Suggested' },
  { id: 'PENDING', label: 'Applied' },
  { id: 'SHORTLISTED', label: 'Shortlisted' },
  { id: 'ACCEPTED_BY_APPLICANT', label: 'Accepted' },
  { id: 'REJECTED_BY_ENTITY', label: 'Rejected' },
  { id: 'OFFERED', label: 'Offered' },
];

const statusFromTab = (tab: SuggestedJobsTabI['id']): ApplicantsStatusI => {
  return tab;
};

const SuggestedJobs: React.FC<SuggestedJobsPropsI> = () => {
  const [activeTab, setActiveTab] =
    useState<SuggestedJobsTabI['id']>('SUGGUESTED');
  const [search, setSearch] = useState('');
  const filterButtonRef = useRef<HTMLButtonElement>(null);

  const {
    filterTabs,
    filterData,
    selectedFilters,
    loading: filterLoading,
    isFilterModalOpen,
    hasActiveFilters,
    activeFilterCount,
    isApplyingFilters,
    openFilterModal,
    closeFilterModal,
    applyFilters,
    clearAllFilters,
  } = useJobFilters({ activeTab });

  const selectedFiltersRef = useRef(selectedFilters);
  selectedFiltersRef.current = selectedFilters;

  const [applicantItems, setApplicantItems] = useState<
    FetchJobsForApplicantResultItemI[]
  >([]);
  const [candidateItems, setCandidateItems] = useState<FetchJobsResultItemI[]>(
    []
  );

  const [nextCursorId, setNextCursorId] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const loadMoreRef = useRef<HTMLDivElement>(null);

  const fetchJobs = useCallback(
    async (isLoadMore = false) => {
      try {
        if (isLoadMore) {
          if (!hasMore || !nextCursorId) return;
          setLoadingMore(true);
        } else {
          setLoading(true);
          setError(null);
        }

        if (activeTab === 'SUGGUESTED') {
          const baseQuery: FetchJobsQueryI = {
            cursorId: isLoadMore && nextCursorId ? String(nextCursorId) : null,
            pageSize: 10,
            // status: 'ACTIVE',
          };

          const currentFilters = selectedFiltersRef.current;
          const filteredPayload = {
            countries:
              currentFilters.locations?.map(f => f.id) || [],
            designations:
              currentFilters.designations?.length > 0
                ? currentFilters.designations.map(f => ({
                    id: f.id,
                    dataType: f.dataType || 'master',
                  }))
                : null,
            shipTypes:
              currentFilters.shipTypes?.length > 0
                ? currentFilters.shipTypes.map(f => ({
                    id: f.id,
                    dataType: f.dataType || 'master',
                  }))
                : null,
            internetLimits:
              currentFilters.internetLimits?.map(f => ({
                id: f.id,
                label: f.label,
              })) || [],
          };

          const res = await fetchJobsForCandidateAPI(
            baseQuery,
            filteredPayload
          );
          setCandidateItems(prev =>
            isLoadMore ? [...prev, ...res.data] : res.data
          );
          setNextCursorId(res.nextCursorId);
          setHasMore(!!res.nextCursorId && res.data.length > 0);
        } else {
          const baseQuery: FetchJobsForApplicantQueryI = {
            cursorId: isLoadMore && nextCursorId ? String(nextCursorId) : null,
            pageSize: 10,
            status: statusFromTab(activeTab),
          };

          const currentFilters = selectedFiltersRef.current;
          const filteredPayload = {
            countries:
              currentFilters.locations?.map(f => f.id) || [],
            designations:
              currentFilters.designations?.length > 0
                ? currentFilters.designations.map(f => ({
                    id: f.id,
                    dataType: f.dataType || 'master',
                  }))
                : null,
            shipTypes:
              currentFilters.shipTypes?.length > 0
                ? currentFilters.shipTypes.map(f => ({
                    id: f.id,
                    dataType: f.dataType || 'master',
                  }))
                : null,
            internetLimits:
              currentFilters.internetLimits?.map(f => ({
                id: f.id,
                label: f.label,
              })) || [],
          };

          console.log('🔍 Fetching applicant jobs with filters:', {
            isLoadMore,
            baseQuery,
            filteredPayload,
            hasFilters: Object.values(currentFilters).some(
              arr => arr.length > 0
            ),
          });

          const res = await fetchJobsForApplicantAPI(
            baseQuery,
            filteredPayload
          );
          setApplicantItems(prev =>
            isLoadMore ? [...prev, ...res.data] : res.data
          );
          setNextCursorId(res.nextCursorId);
          setHasMore(!!res.nextCursorId && res.data.length > 0);
        }
      } catch (e) {
        console.error('Failed to fetch jobs', e);

        let errorMessage = 'Failed to load jobs. Please try again.';
        if (e instanceof TypeError) {
          errorMessage =
            'Network error. Please check your connection and try again.';
        } else if (e && typeof e === 'object' && 'status' in e) {
          const status = (e as { status: number }).status;
          if (status === 401) {
            errorMessage = 'Authentication failed. Please log in again.';
          } else if (status === 403) {
            errorMessage = 'You do not have permission to view these jobs.';
          } else if (status >= 500) {
            errorMessage = 'Server error. Please try again later.';
          }
        }

        setError(errorMessage);
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [activeTab, nextCursorId, hasMore]
  );

  const retryFetch = useCallback(() => {
    setError(null);
    fetchJobs(false);
  }, [fetchJobs]);

  useEffect(() => {
    setApplicantItems([]);
    setCandidateItems([]);
    setNextCursorId(null);
    setHasMore(true);
    setError(null);
    fetchJobs(false);
  }, [activeTab]);

  useEffect(() => {
    console.log('🔄 Filters changed, refetching jobs:', {
      selectedFilters,
      hasActiveFilters,
      filterCount: Object.values(selectedFilters).reduce(
        (total, arr) => total + arr.length,
        0
      ),
    });
    setApplicantItems([]);
    setCandidateItems([]);
    setNextCursorId(null);
    setHasMore(true);
    fetchJobs(false);
  }, [selectedFilters]);

  useEffect(() => {
    const el = loadMoreRef.current;
    if (!el) return;
    const observer = new IntersectionObserver(
      entries => {
        const [entry] = entries;
        if (entry.isIntersecting && hasMore && !loading && !loadingMore) {
          fetchJobs(true);
        }
      },
      { root: null, rootMargin: '100px', threshold: 0.1 }
    );
    observer.observe(el);
    return () => {
      observer.disconnect();
    };
  }, [hasMore, loading, loadingMore]);

  const jobs: SuggestedJobItemI[] = useMemo(() => {
    const currentItems =
      activeTab === 'SUGGUESTED' ? candidateItems : applicantItems;

    let mapped = currentItems.map(j => ({
      id: j.id,
      slug: j.id,
      title: j.designation?.name || 'Job',
      company: j.entity?.name || '—',
      postedAt: j.createdAt ? formatDate(j.createdAt) : '',
      avatarUrl: null,
      matching: j.matching || null,
    }));

    if (activeTab === 'SUGGUESTED') {
      mapped = mapped.sort((a, b) => {
        const aMatch = a.matching || 0;
        const bMatch = b.matching || 0;
        return bMatch - aMatch;
      });
    }

    const s = search.trim().toLowerCase();
    if (!s) return mapped;
    return mapped.filter(
      j =>
        j.title.toLowerCase().includes(s) || j.company.toLowerCase().includes(s)
    );
  }, [activeTab, candidateItems, applicantItems, search]);

  const { isAuthenticated } = useAuth();

  return (
    <section className="lg:col-span-6 col-span-1">
      <div className="space-y-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-bold text-xl">Suggested Jobs</h3>
          <div className="flex items-center gap-3">
            {activeTab === 'SUGGUESTED' && (
              <button
                onClick={retryFetch}
                disabled={loading}
                className="text-gray-500 hover:text-gray-700 disabled:opacity-50"
                title="Refresh suggestions"
              >
                <svg
                  className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
              </button>
            )}
            {isAuthenticated && (
              <Link
                className="text-primary font-medium text-base"
                href={'/myjobs'}
              >
                My Jobs
              </Link>
            )}
          </div>
        </div>
        <div className="bg-white border border-gray-200 rounded-2xl shadow-sm ">
          <div className="px-2 sm:px-4 pt-4">
            <Tabs
              items={tabs}
              activeId={activeTab}
              onChange={id => setActiveTab(id as SuggestedJobsTabI['id'])}
            />
          </div>

          <div className="px-4 py-3">
            <SearchInput
              value={search}
              onChange={e => setSearch(e.target.value)}
              rightSlot={
                <div className="flex items-center gap-2 relative">
                  <button
                    ref={filterButtonRef}
                    onClick={openFilterModal}
                    disabled={isApplyingFilters}
                    className="relative p-2 rounded-md hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Filter jobs"
                  >
                    {isApplyingFilters ? (
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-600"></div>
                    ) : (
                      <Filter />
                    )}
                    {hasActiveFilters && !isApplyingFilters && (
                      <span className="absolute -top-1 -right-1 inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-bold leading-none text-white bg-primary rounded-full min-w-[18px]">
                        {activeFilterCount}
                      </span>
                    )}
                  </button>
                  {hasActiveFilters && (
                    <button
                      onClick={() => clearAllFilters(true)}
                      disabled={isApplyingFilters}
                      className="text-xs text-gray-500 hover:text-gray-700 px-2 py-1 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Clear all filters"
                    >
                      {isApplyingFilters ? 'Clearing...' : 'Clear'}
                    </button>
                  )}

                  <FilterModal
                    isOpen={isFilterModalOpen}
                    onClose={closeFilterModal}
                    onApply={applyFilters}
                    filterTabs={filterTabs}
                    filterData={filterData}
                    currentFilters={selectedFilters}
                    loading={filterLoading}
                    triggerRef={filterButtonRef}
                  />
                </div>
              }
            />
          </div>

          {loading && jobs.length === 0 ? (
            <ul className="divide-y divide-gray-200">
              {Array.from({ length: 5 }).map((_, index) => (
                <JobItemSkeleton key={index} />
              ))}
            </ul>
          ) : error ? (
            <div className="px-4 py-12 text-center">
              <div className="max-w-sm mx-auto">
                <p className="text-red-500 mb-4">{error}</p>
                <button
                  onClick={retryFetch}
                  disabled={loading}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin -ml-1 mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                      Retrying...
                    </>
                  ) : (
                    'Try Again'
                  )}
                </button>
              </div>
            </div>
          ) : jobs.length === 0 ? (
            <div className="px-4 py-12 text-center text-gray-500">
              <div className="max-w-sm mx-auto">
                {search.trim() ? (
                  <>
                    <p className="mb-2">
                      No jobs found matching "{search.trim()}"
                    </p>
                    <p className="text-sm">Try adjusting your search terms</p>
                  </>
                ) : activeTab === 'SUGGUESTED' ? (
                  <>
                    <p className="mb-2">No suggested jobs available</p>
                    <p className="text-sm">
                      Check back later for new opportunities that match your
                      profile
                    </p>
                  </>
                ) : (
                  <>
                    <p className="mb-2">
                      No{' '}
                      {tabs.find(t => t.id === activeTab)?.label.toLowerCase()}{' '}
                      jobs
                    </p>
                    <p className="text-sm">
                      Jobs will appear here when you{' '}
                      {activeTab === 'PENDING'
                        ? 'apply to them'
                        : 'have activity'}
                    </p>
                  </>
                )}
              </div>
            </div>
          ) : (
            <ul className="divide-y divide-gray-200">
              {jobs.map(job => (
                <li key={`${activeTab}-${job.id}`} className="px-4">
                  <Link
                    href={`/jobs/${job.slug}`}
                    className="block hover:bg-gray-50 rounded-lg"
                  >
                    <div className="flex gap-4 py-4">
                      <div className="h-8 w-8 rounded-full overflow-hidden flex-shrink-0 bg-gray-100" />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-semibold text-gray-900 truncate">
                              {job.title}
                            </p>
                            <p className="text-sm text-gray-700 truncate">
                              {job.company}
                            </p>
                            <p className="text-xs text-gray-500 mt-1">
                              {job.postedAt}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                </li>
              ))}
            </ul>
          )}

          <div ref={loadMoreRef} />
          {loadingMore && (
            <div className="px-4 py-4 text-center">
              <div className="inline-flex items-center text-sm text-gray-500">
                <div className="animate-spin -ml-1 mr-2 h-4 w-4 border-2 border-gray-300 border-t-primary rounded-full" />
                Loading more jobs...
              </div>
            </div>
          )}

          {!hasMore && jobs.length > 0 && !loading && !loadingMore && (
            <div className="px-4 py-4 text-center text-sm text-gray-400">
              You've reached the end of the list
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default SuggestedJobs;
