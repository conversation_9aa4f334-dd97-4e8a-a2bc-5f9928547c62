'use client';

import React, { useEffect, useState, useRef } from 'react';
import { FilterItemI, FilterTabI } from '@/networks/jobs/types';
import Tabs from '../Tabs';

export type FilterModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onApply: (filters: Record<string, FilterItemI[]>) => void;
  filterTabs: FilterTabI[];
  filterData: Record<string, FilterItemI[]>;
  currentFilters?: Record<string, FilterItemI[]>; // Current selected filters from parent
  loading?: boolean;
  className?: string;
  triggerRef?: React.RefObject<HTMLElement | null>;
};

const FilterModal: React.FC<FilterModalProps> = ({
  isOpen,
  onClose,
  onApply,
  filterTabs,
  filterData,
  currentFilters = {},
  loading = false,
  className = '',
  triggerRef,
}) => {
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [activeTab, setActiveTab] = useState<string>(filterTabs[0]?.id || '');
  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, FilterItemI[]>
  >({});
  const [hasChanges, setHasChanges] = useState(false);
  const [position, setPosition] = useState<'left' | 'right'>('right');

  useEffect(() => {
    if (filterTabs.length > 0 && !activeTab) {
      setActiveTab(filterTabs[0].id);
    }
  }, [filterTabs, activeTab]);

  useEffect(() => {
    if (isOpen) {
      setSelectedFilters(currentFilters);
      setHasChanges(false);
    }
  }, [isOpen, currentFilters]);

  useEffect(() => {
    if (isOpen && triggerRef?.current && dropdownRef.current) {
      const triggerRect = triggerRef.current.getBoundingClientRect();
      const dropdownWidth = 384; // w-96 = 384px
      const viewportWidth = window.innerWidth;
      const spaceOnRight = viewportWidth - triggerRect.right;

      if (spaceOnRight < dropdownWidth && triggerRect.left > dropdownWidth) {
        setPosition('left');
      } else {
        setPosition('right');
      }
    }
  }, [isOpen, triggerRef]);

  // Close dropdown when clicking outside or pressing Escape
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        triggerRef?.current &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleKeyDown);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [isOpen, onClose, triggerRef]);

  if (!isOpen) return null;

  const handleFilterToggle = (tabId: string, filter: FilterItemI) => {
    setSelectedFilters(prev => {
      const currentFilters = prev[tabId] || [];
      const isSelected = currentFilters.some(f => f.id === filter.id);

      const newFilters = isSelected
        ? currentFilters.filter(f => f.id !== filter.id)
        : [...currentFilters, filter];

      const updated = { ...prev, [tabId]: newFilters };
      setHasChanges(true);
      return updated;
    });
  };

  const handleApply = () => {
    onApply(selectedFilters);
    setHasChanges(false);
    onClose();
  };

  const handleClear = () => {
    setSelectedFilters({});
    setHasChanges(true);
  };

  const isFilterSelected = (tabId: string, filterId: string) => {
    return selectedFilters[tabId]?.some(f => f.id === filterId) || false;
  };

  const activeTabFilters = filterData[activeTab] || [];
  const totalSelectedCount = Object.values(selectedFilters).reduce(
    (total, filters) => total + filters.length,
    0
  );

  return (
    <div
      ref={dropdownRef}
      className={`absolute top-full mt-2 bg-white rounded-lg shadow-lg border border-gray-200 z-[9999]
        w-80 sm:w-96
        max-w-[calc(100vw-2rem)]
        max-h-[70vh] sm:max-h-[80vh]
        overflow-hidden ${className} ${
          position === 'right' ? 'right-0' : 'left-0'
        }`}
      role="dialog"
      aria-modal="true"
    >
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">Filter Jobs</h3>
        <button
          type="button"
          onClick={onClose}
          className="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      {loading ? (
        <div className="flex items-center justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-3 text-gray-600">Loading filters...</span>
        </div>
      ) : (
        <>
          <div className="bg-gray-50 px-4 pt-2">
            <Tabs
              items={filterTabs.map(tab => ({
                id: tab.id,
                label: tab.label,
                badge: tab.appliedCount > 0 ? tab.appliedCount : undefined,
              }))}
              activeId={activeTab}
              onChange={setActiveTab}
            />
          </div>

          <div className="flex-1 overflow-y-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none] scrollbar-hide p-4 max-h-60 sm:max-h-80">
            <div className="grid grid-cols-1 gap-2">
              {activeTabFilters.map(filter => {
                const isSelected = isFilterSelected(activeTab, filter.id);
                return (
                  <button
                    key={filter.id}
                    onClick={() => handleFilterToggle(activeTab, filter)}
                    className={`flex items-center justify-between p-3 rounded-lg border transition-all text-left ${
                      isSelected
                        ? 'border-primary bg-primary/5 text-primary'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <span className="text-sm font-medium truncate">
                      {filter.label}
                    </span>
                    <span className="text-xs text-gray-500 ml-2">
                      ({filter.count})
                    </span>
                  </button>
                );
              })}
            </div>

            {activeTabFilters.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <p>No filters available for this category</p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center space-x-3">
              <button
                type="button"
                onClick={handleClear}
                disabled={totalSelectedCount === 0}
                className="text-sm text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Clear All
              </button>
              {totalSelectedCount > 0 && (
                <span className="text-sm text-gray-600">
                  {totalSelectedCount} filter
                  {totalSelectedCount !== 1 ? 's' : ''} selected
                </span>
              )}
            </div>
            <div className="flex items-center space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-3 py-1.5 rounded-md text-sm border border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleApply}
                disabled={!hasChanges}
                className={`px-3 py-1.5 rounded-md text-sm text-white bg-primary hover:bg-primary-dark disabled:opacity-60 disabled:cursor-not-allowed`}
              >
                Apply
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default FilterModal;
