import { z } from 'zod';
import { CursorPaginationSchema, UUIDSchema } from '../common/common';
import { EntityTypeE } from '@prisma/postgres';
import { EntityProfileEmailTypeE } from '@consts/company/entityProfile';
import { OTPSchema } from '@navicater/b2c-internal-communication';

export const EntityAboutFetchSchema = z
  .object({
    entityId: UUIDSchema.optional(),
    entityRawDataId: UUIDSchema.optional(),
  })
  .refine((data) => data.entityId || data.entityRawDataId, {
    message: 'Either entityId or entityRawDataId must be provided.',
  })
  .refine((data) => !(data.entityId && data.entityRawDataId), {
    message: 'Only one of entityId or entityRawDataId must be provided.',
  });
export type EntityAboutFetchParamsI = z.infer<typeof EntityAboutFetchSchema>;

export const EntityAboutUpdateSchema = z.object({
  description: z.string().max(511).nullable().optional(),
  website: z.string().url().max(255).nullable().optional(),
  overview: z.string().max(1000).nullable().optional(),
  avatar: z.string().nullable().optional(),
  foundedAt: z
    .union([
      z.string().nullable().optional(),
      z.null(),
      z.undefined()
    ])
    .refine((val) => {
      if (val === null || val === undefined || val === '') return true;
      return !isNaN(Date.parse(val));
    }, {
      message: 'Invalid date format',
    })
    .transform((val) => {
      if (val === null || val === undefined || val === '') return null;
      return new Date(val);
    }),
});

export type EntityAboutUpdateParamsI = z.infer<typeof EntityAboutUpdateSchema>;

export const EntityTabsFetchSchema = z
  .object({
    entityId: UUIDSchema.optional(),
    entityRawDataId: UUIDSchema.optional(),
  })
  .refine((data) => data.entityId || data.entityRawDataId, {
    message: 'Either entityId or entityRawDataId must be provided.',
  })
  .refine((data) => !(data.entityId && data.entityRawDataId), {
    message: 'Only one of entityId or entityRawDataId must be provided.',
  });

export type EntityTabsFetchParamsI = z.infer<typeof EntityTabsFetchSchema>;

export const EntityTabsUpdateSchema = z
  .object({
    entityId: UUIDSchema.optional(),
    entityRawDataId: UUIDSchema.optional(),
    peopleTab: z.boolean().optional(),
    alumniTab: z.boolean().optional(),
    jobPostingTab: z.boolean().optional(),
  })
  .refine((data) => data.entityId || data.entityRawDataId, {
    message: 'Either entityId or entityRawDataId must be provided.',
  })
  .refine((data) => !(data.entityId && data.entityRawDataId), {
    message: 'Only one of entityId or entityRawDataId must be provided.',
  });

export type EntityTabsUpdateParamsI = z.infer<typeof EntityTabsUpdateSchema>;

export const EntityCreateOneParamsSchema = z.object({
  entityId: UUIDSchema.optional(),
  entityRawDataId: UUIDSchema.optional(),
  profileId: UUIDSchema,
  name: z.string(),
  type: z.nativeEnum(EntityTypeE),
  website: z.string().url().optional(),
  admins: z.array(UUIDSchema),
  email: z.string().email(),
  emailType: EntityProfileEmailTypeE,
  purpose: z.string().optional(),
});

export type EntityCreateOneParamsI = z.infer<typeof EntityCreateOneParamsSchema>;

export const EntityProfileIdSchema = z.object({
  entityProfileId: UUIDSchema,
});
export type EntityProfileIdI = z.infer<typeof EntityProfileIdSchema>;

export const VerifyOTPForEntityProfileVerificationSchema = z.object({
  entityProfileId: UUIDSchema,
  otp: OTPSchema,
});
export type VerifyOTPForEntityProfileVerificationI = z.infer<typeof VerifyOTPForEntityProfileVerificationSchema>;

export const EntityProfileIdCursorPaginationSchema = CursorPaginationSchema.merge(EntityProfileIdSchema);

export type EntityProfileIdCursorPaginationI = z.infer<typeof EntityProfileIdCursorPaginationSchema>;
