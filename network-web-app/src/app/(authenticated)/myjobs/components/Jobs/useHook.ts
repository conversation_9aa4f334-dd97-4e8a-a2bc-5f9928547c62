import { useState, useCallback, useMemo } from 'react';
import { fetchFiltersForEntityMemberAPI } from '@/networks/jobs';
import type {
  JobFiltersI,
  FilterItemI,
  FilterTabI,
  FetchFiltersForEntityMemberQueryI,
  JobStatusI,
  IdTypeI,
} from '@/networks/jobs/types';

export const useJobFilters = () => {
  const [filterData, setFilterData] = useState<Record<string, FilterItemI[]>>({});
  const [selectedFilters, setSelectedFilters] = useState<Record<string, FilterItemI[]>>({});
  const [filterLoading, setFilterLoading] = useState(false);

  const isEntityProfile =
    typeof window !== 'undefined' &&
    localStorage.getItem('activeProfileType') === 'ENTITY';

  const entityProfileId =
    typeof window !== 'undefined'
      ? localStorage.getItem('entityProfileId')
      : null;

  const fetchFilters = useCallback(async (status: JobStatusI) => {
    try {
      setFilterLoading(true);

      const query: FetchFiltersForEntityMemberQueryI = {
        // status,
        isOfficial: isEntityProfile,
        ...(isEntityProfile && entityProfileId
          ? { entity: { id: entityProfileId, dataType: 'master' as const } }
          : {}),
      };

      const filters = await fetchFiltersForEntityMemberAPI(query);

      // Transform the filters to match the expected format
      const transformedFilters: Record<string, FilterItemI[]> = {
        designations: filters.designations || [],
        shipTypes: filters.shipTypes || [],
      };

      setFilterData(transformedFilters);
    } catch (error) {
      console.error('Failed to fetch filters:', error);
      setFilterData({
        designations: [],
        shipTypes: [],
      });
    } finally {
      setFilterLoading(false);
    }
  }, [isEntityProfile, entityProfileId]);

  const filterTabs: FilterTabI[] = useMemo(() => {
    const getAppliedCount = (tabId: string) => {
      return selectedFilters[tabId]?.length || 0;
    };

    return [
      {
        id: 'designations' as const,
        label: 'Designations',
        appliedCount: getAppliedCount('designations'),
      },
      {
        id: 'shipTypes' as const,
        label: 'Ship Types',
        appliedCount: getAppliedCount('shipTypes'),
      },
    ];
  }, [selectedFilters]);

  const applyFilters = useCallback((filters: Record<string, FilterItemI[]>) => {
    setSelectedFilters(filters);
  }, []);

  const clearAllFilters = useCallback((shouldRefetch = false) => {
    setSelectedFilters({});
    return shouldRefetch;
  }, []);

  const hasActiveFilters = useMemo(() => {
    return Object.values(selectedFilters).some(filters => filters.length > 0);
  }, [selectedFilters]);

  const getFilterPayload = useCallback(() => {
    return {
      designations: selectedFilters.designations?.map(filter => ({
        id: filter.id,
        dataType: filter.dataType || 'master',
      })) || [],
      shipTypes: selectedFilters.shipTypes?.map(filter => ({
        id: filter.id,
        dataType: filter.dataType || 'master',
      })) || [],
    };
  }, [selectedFilters]);

  return {
    filterData,
    selectedFilters,
    filterLoading,
    filterTabs,
    fetchFilters,
    applyFilters,
    clearAllFilters,
    hasActiveFilters,
    getFilterPayload,
  };
};
