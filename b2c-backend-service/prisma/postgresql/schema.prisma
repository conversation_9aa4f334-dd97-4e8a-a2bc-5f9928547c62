generator client {
  provider        = "prisma-client-js"
  output          = "../../node_modules/@prisma/postgres"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_DATABASE_URL")
  schemas  = ["announcement", "auth", "app", "career", "company", "document", "feed", "forum", "leaderboard", "master", "network", "port", "rawData", "score", "ship", "user", "referral", "news"]
}

//#region announcement
model Announcement {
  id                  String   @default(uuid()) @db.Uuid
  profileId           String?  @db.Uuid
  entityProfileId     String?  @db.Uuid
  cursorId            BigInt   @default(autoincrement()) @db.BigInt()
  title               String   @db.VarChar(100)
  description         String   @db.VarChar(1000)
  latitude            Decimal? @db.Decimal(8, 6)
  longitude           Decimal? @db.Decimal(9, 6)
  addressRawDataId    String?  @db.Uuid
  cityId              String?  @db.Uuid
  cityRawDataId       String?  @db.Uuid
  portUnLocode        String?  @db.VarChar(5)
  portRawDataUnLocode String?  @db.VarChar(5)
  countryIso2         String?  @db.Char(2)
  startDate           DateTime @db.Date()
  endDate             DateTime @db.Date()
  startTime           DateTime @db.Time()
  endTime             DateTime @db.Time()
  totalAttendees      Int      @default(0)
  createdAt           DateTime @default(now()) @db.Timestamp()
  updatedAt           DateTime @updatedAt @db.Timestamp()

  Profile        Profile?        @relation(fields: [profileId], references: [id], onDelete: Cascade)
  EntityProfile  EntityProfile?  @relation(fields: [entityProfileId], references: [id], onDelete: Cascade)
  AddressRawData AddressRawData? @relation(fields: [addressRawDataId], references: [id], onDelete: Cascade)
  City           City?           @relation(fields: [cityId], references: [id], onDelete: Cascade)
  CityRawData    CityRawData?    @relation(fields: [cityRawDataId], references: [id], onDelete: Cascade)
  Port           Port?           @relation(fields: [portUnLocode], references: [unLocode], onDelete: Cascade)
  PortRawData    PortRawData?    @relation(fields: [portRawDataUnLocode], references: [unLocode], onDelete: Cascade)
  Country        Country?        @relation(fields: [countryIso2], references: [iso2], onDelete: Cascade)
  RSVP           RSVP[]

  @@id([id])
  @@schema("announcement")
}

model RSVP {
  id              String         @default(uuid()) @db.Uuid
  cursorId        BigInt         @default(autoincrement()) @db.BigInt()
  profileId       String?        @db.Uuid
  entityProfileId String?        @db.Uuid
  announcementId  String         @db.Uuid
  createdAt       DateTime       @default(now()) @db.Timestamp()
  Announcement    Announcement   @relation(fields: [announcementId], references: [id], onDelete: Cascade)
  Profile         Profile?       @relation(fields: [profileId], references: [id], onDelete: Cascade)
  EntityProfile   EntityProfile? @relation(fields: [entityProfileId], references: [id], onDelete: Cascade)

  @@id([id])
  @@schema("announcement")
}

//#endregion announcement
//#region app
enum PlatformE {
  android
  ios
  web_app

  @@schema("app")
}

enum PolicyTypeE {
  SIGNUP_PRIVACY_POLICY
  TERMS_OF_USE
  REFERRAL_TERMS

  @@schema("app")
}

model Platform {
  id        PlatformE
  url       String    @db.VarChar(255)
  createdAt DateTime  @default(now()) @db.Timestamp()
  updatedAt DateTime  @updatedAt @db.Timestamp()
  Session   Session[]

  @@id([id])
  @@schema("app")
}

model AppVersion {
  id         String    @default(uuid()) @db.Uuid
  platformId PlatformE
  versionNo  String    @db.VarChar(15)
  isActive   Boolean   @default(true)
  createdAt  DateTime  @default(now()) @db.Timestamp()
  updatedAt  DateTime  @updatedAt @db.Timestamp()
  Session    Session[]

  @@id([id])
  @@unique([platformId, versionNo])
  @@schema("app")
}

model Policy {
  id        String       @default(uuid()) @db.Uuid
  content   String       @db.Text
  type      PolicyTypeE?
  isActive  Boolean
  createdAt DateTime     @default(now()) @db.Timestamptz()
  updatedAt DateTime     @updatedAt @db.Timestamptz()

  @@id([id])
  @@schema("app")
}

//#endregion app

//#region auth

enum ProfileTypeE {
  USER
  ENTITY

  @@schema("auth")
}

model Session {
  id           String       @default(uuid()) @db.Uuid
  cursorId     BigInt       @default(autoincrement()) @db.BigInt()
  profileId    String       @db.Uuid
  ipAddress    String       @db.VarChar(39)
  platformId   PlatformE
  appVersionId String       @db.Uuid
  deviceId     String       @db.Uuid
  deviceToken  String?      @db.Text
  latitude     Decimal?     @db.Decimal(8, 6)
  longitude    Decimal?     @db.Decimal(9, 6)
  expiryDate   DateTime
  profileType  ProfileTypeE @default(USER)
  isActive     Boolean      @default(true)
  createdAt    DateTime     @default(now()) @db.Timestamp()
  lastActivity DateTime     @updatedAt @db.Timestamp()

  Platform   Platform   @relation(fields: [platformId], references: [id])
  AppVersion AppVersion @relation(fields: [appVersionId], references: [id])

  @@id([id])
  @@schema("auth")
}

//#endregion auth
//#region career
model ProfileEducation {
  id              String         @id @default(uuid()) @db.Uuid
  cursorId        BigInt         @default(autoincrement()) @db.BigInt()
  profileId       String         @db.Uuid
  entityId        String?        @db.Uuid
  entityRawDataId String?        @db.Uuid
  degreeId        String?        @db.Uuid
  degreeRawDataId String?        @db.Uuid
  fromDate        DateTime       @db.Date
  toDate          DateTime?      @db.Date
  createdAt       DateTime       @default(now()) @db.Timestamp()
  updatedAt       DateTime       @updatedAt @db.Timestamp()
  Degree          Degree?        @relation(fields: [degreeId], references: [id])
  DegreeRawData   DegreeRawData? @relation(fields: [degreeRawDataId], references: [id])
  Entity          Entity?        @relation(fields: [entityId], references: [id])
  EntityRawData   EntityRawData? @relation(fields: [entityRawDataId], references: [id])
  Profile         Profile        @relation(fields: [profileId], references: [id])

  @@schema("career")
}

model ProfileSkill {
  id             String        @id @default(uuid()) @db.Uuid
  skillId        String?       @db.Uuid
  skillRawDataId String?       @db.Uuid
  profileId      String        @db.Uuid
  createdAt      DateTime      @default(now()) @db.Timestamp()
  updatedAt      DateTime      @updatedAt @db.Timestamp()
  Skill          Skill?        @relation(fields: [skillId], references: [id], onDelete: Cascade)
  SkillRawData   SkillRawData? @relation(fields: [skillRawDataId], references: [id], onDelete: Cascade)
  Profile        Profile       @relation(fields: [profileId], references: [id])

  @@schema("career")
}

model ProfileSkillEntityDegree {
  id              String         @id @default(uuid()) @db.Uuid
  skillId         String?        @db.Uuid
  skillRawDataId  String?        @db.Uuid
  entityId        String?        @db.Uuid
  entityRawDataId String?        @db.Uuid
  degreeId        String?        @db.Uuid
  degreeRawDataId String?        @db.Uuid
  profileId       String         @db.Uuid
  createdAt       DateTime       @default(now()) @db.Timestamp()
  updatedAt       DateTime       @updatedAt @db.Timestamp()
  Degree          Degree?        @relation(fields: [degreeId], references: [id])
  DegreeRawData   DegreeRawData? @relation(fields: [degreeRawDataId], references: [id])
  Entity          Entity?        @relation(fields: [entityId], references: [id])
  EntityRawData   EntityRawData? @relation(fields: [entityRawDataId], references: [id])
  Skill           Skill?         @relation(fields: [skillId], references: [id])
  SkillRawData    SkillRawData?  @relation(fields: [skillRawDataId], references: [id])

  @@schema("career")
}

model ProfileSkillEntityCertificate {
  id                   String         @id @default(uuid()) @db.Uuid
  skillId              String?        @db.Uuid
  skillRawDataId       String?        @db.Uuid
  entityId             String?        @db.Uuid
  entityRawDataId      String?        @db.Uuid
  certificateId        String?        @db.Uuid
  certificateRawDataId String?        @db.Uuid
  profileId            String         @db.Uuid
  createdAt            DateTime       @default(now()) @db.Timestamp()
  updatedAt            DateTime       @updatedAt @db.Timestamp()
  Entity               Entity?        @relation(fields: [entityId], references: [id])
  EntityRawData        EntityRawData? @relation(fields: [entityRawDataId], references: [id])
  Skill                Skill?         @relation(fields: [skillId], references: [id])
  SkillRawData         SkillRawData?  @relation(fields: [skillRawDataId], references: [id])
  Profile              Profile        @relation(fields: [profileId], references: [id])

  @@schema("career")
}

model ProfileSkillExperienceShip {
  id               String   @id @default(uuid()) @db.Uuid
  skillId          String?  @db.Uuid
  skillRawDataId   String?  @db.Uuid
  experienceShipId String   @db.Uuid
  profileId        String   @db.Uuid
  createdAt        DateTime @default(now()) @db.Timestamp()
  updatedAt        DateTime @updatedAt @db.Timestamp()

  Skill        Skill?        @relation(fields: [skillId], references: [id])
  SkillRawData SkillRawData? @relation(fields: [skillRawDataId], references: [id])

  ExperienceShip ExperienceShip? @relation(fields: [experienceShipId], references: [id])

  @@schema("career")
}

model ProfileCertificate {
  id                         String                    @id @default(uuid()) @db.Uuid
  profileId                  String                    @db.Uuid
  entityId                   String?                   @db.Uuid
  entityRawDataId            String?                   @db.Uuid
  certificateCourseId        String?                   @db.Uuid
  certificateCourseRawDataId String?                   @db.Uuid
  fromDate                   DateTime                  @db.Date
  untilDate                  DateTime?                 @db.Date
  fileUrl                    String?                   @db.Text
  createdAt                  DateTime                  @default(now()) @db.Timestamp()
  updatedAt                  DateTime                  @updatedAt @db.Timestamp()
  CertificateCourse          CertificateCourse?        @relation(fields: [certificateCourseId], references: [id])
  CertificateCourseRawData   CertificateCourseRawData? @relation(fields: [certificateCourseRawDataId], references: [id])
  Entity                     Entity?                   @relation(fields: [entityId], references: [id])
  EntityRawData              EntityRawData?            @relation(fields: [entityRawDataId], references: [id])
  Profile                    Profile                   @relation(fields: [profileId], references: [id])

  @@schema("career")
}

model Experience {
  id                    String                  @id @default(uuid()) @db.Uuid
  cursorId              BigInt                  @default(autoincrement()) @db.BigInt()
  profileId             String                  @db.Uuid
  entityId              String?                 @db.Uuid
  entityRawDataId       String?                 @db.Uuid
  years                 Int                     @default(0)
  months                Int                     @default(0)
  sailingYears          Int                     @default(0)
  sailingMonths         Int                     @default(0)
  Entity                Entity?                 @relation(fields: [entityId], references: [id])
  EntityRawData         EntityRawData?          @relation(fields: [entityRawDataId], references: [id])
  Profile               Profile                 @relation(fields: [profileId], references: [id])
  ExperienceDesignation ExperienceDesignation[]

  @@schema("career")
}

model ExperienceDesignation {
  id                       String                  @id @default(uuid()) @db.Uuid
  profileId                String                  @db.Uuid
  experienceId             String                  @db.Uuid
  designationAlternativeId String?                 @db.Uuid
  designationRawDataId     String?                 @db.Uuid
  fromDate                 DateTime                @db.Date
  toDate                   DateTime?               @db.Date
  createdAt                DateTime                @default(now()) @db.Timestamptz()
  updatedAt                DateTime                @updatedAt @db.Timestamptz()
  DesignationAlternative   DesignationAlternative? @relation(fields: [designationAlternativeId], references: [id])
  DesignationRawData       DesignationRawData?     @relation(fields: [designationRawDataId], references: [id])
  Experience               Experience              @relation(fields: [experienceId], references: [id], onDelete: Cascade)
  ExperienceShip           ExperienceShip[]
  Profile                  Profile                 @relation(fields: [profileId], references: [id])

  @@schema("career")
}

model ExperienceShip {
  id                          String                        @id @default(uuid()) @db.Uuid
  profileId                   String                        @db.Uuid
  experienceDesignationId     String                        @db.Uuid
  shipImo                     String?                       @db.VarChar(7)
  shipRawDataImo              String?                       @db.VarChar(7)
  name                        String                        @db.VarChar(100)
  subVesselTypeId             String?                       @db.Uuid
  subVesselTypeRawDataId      String?                       @db.Uuid
  sizeGt                      Decimal                       @db.Decimal(10, 2)
  powerKw                     Decimal?                      @db.Decimal(8, 2)
  dwt                         Int?                          @db.Integer
  details                     String?                       @db.VarChar(255)
  fromDate                    DateTime                      @db.Date
  toDate                      DateTime?                     @db.Date
  departmentAlternativeId     String?                       @db.Uuid
  departmentRawDataId         String?                       @db.Uuid
  createdAt                   DateTime                      @default(now()) @db.Timestamptz()
  updatedAt                   DateTime                      @updatedAt @db.Timestamptz()
  ExperienceCargo             ExperienceCargo[]
  ExperienceEquipmentCategory ExperienceEquipmentCategory[]
  DepartmentAlternative       DepartmentAlternative?        @relation(fields: [departmentAlternativeId], references: [id])
  DepartmentRawData           DepartmentRawData?            @relation(fields: [departmentRawDataId], references: [id])
  ExperienceDesignation       ExperienceDesignation         @relation(fields: [experienceDesignationId], references: [id], onDelete: Cascade)
  Ship                        Ship?                         @relation(fields: [shipImo], references: [imo])
  ShipRawData                 ShipRawData?                  @relation(fields: [shipRawDataImo], references: [imo])
  SubVesselType               SubVesselType?                @relation(fields: [subVesselTypeId], references: [id])
  SubVesselTypeRawData        SubVesselTypeRawData?         @relation(fields: [subVesselTypeRawDataId], references: [id])
  Profile                     Profile                       @relation(fields: [profileId], references: [id])
  ProfileSkillExperienceShip  ProfileSkillExperienceShip[]

  @@schema("career")
}

model ExperienceEquipmentCategory {
  id                         String                    @id @default(uuid()) @db.Uuid
  profileId                  String?                   @db.Uuid
  experienceShipId           String                    @db.Uuid
  equipmentCategoryId        String?                   @db.Uuid
  equipmentCategoryRawDataId String?                   @db.Uuid
  manufacturerName           String                    @db.VarChar(100)
  model                      String                    @db.VarChar(100)
  powerCapacity              Decimal?                  @db.Decimal(8, 2)
  details                    String?                   @db.VarChar(150)
  createdAt                  DateTime                  @default(now()) @db.Timestamptz()
  updatedAt                  DateTime                  @updatedAt @db.Timestamptz()
  EquipmentCategory          EquipmentCategory?        @relation(fields: [equipmentCategoryId], references: [id])
  EquipmentCategoryRawData   EquipmentCategoryRawData? @relation(fields: [equipmentCategoryRawDataId], references: [id])
  ExperienceShip             ExperienceShip            @relation(fields: [experienceShipId], references: [id], onDelete: Cascade)
  Profile                    Profile?                  @relation(fields: [profileId], references: [id])
  ExperienceFuelType         ExperienceFuelType[]

  @@schema("career")
}

model ExperienceEquipmentManufacturer {
  id                             String                        @id @default(uuid()) @db.Uuid
  profileId                      String                        @db.Uuid
  experienceShipId               String                        @db.Uuid
  equipmentManufacturerId        String?                       @db.Uuid
  equipmentManufacturerRawDataId String?                       @db.Uuid
  createdAt                      DateTime                      @default(now()) @db.Timestamptz()
  updatedAt                      DateTime                      @updatedAt @db.Timestamptz()
  EquipmentManufacturer          EquipmentManufacturer?        @relation(fields: [equipmentManufacturerId], references: [id])
  EquipmentManufacturerRawData   EquipmentManufacturerRawData? @relation(fields: [equipmentManufacturerRawDataId], references: [id])
  Profile                        Profile                       @relation(fields: [profileId], references: [id])

  @@schema("career")
}

model ExperienceEquipmentModel {
  id                      String                 @id @default(uuid()) @db.Uuid
  profileId               String                 @db.Uuid
  experienceShipId        String                 @db.Uuid
  equipmentModelId        String?                @db.Uuid
  equipmentModelRawDataId String?                @db.Uuid
  createdAt               DateTime               @default(now()) @db.Timestamptz()
  updatedAt               DateTime               @updatedAt @db.Timestamptz()
  EquipmentModel          EquipmentModel?        @relation(fields: [equipmentModelId], references: [id])
  EquipmentModelRawData   EquipmentModelRawData? @relation(fields: [equipmentModelRawDataId], references: [id])
  Profile                 Profile                @relation(fields: [profileId], references: [id])

  @@schema("career")
}

model ExperienceFuelType {
  id                            String                      @id @default(uuid()) @db.Uuid
  experienceEquipmentCategoryId String                      @db.Uuid
  fuelTypeId                    String?                     @db.Uuid
  fuelTypeRawDataId             String?                     @db.Uuid
  ExperienceEquipmentCategory   ExperienceEquipmentCategory @relation(fields: [experienceEquipmentCategoryId], references: [id], onDelete: Cascade)
  FuelType                      FuelType?                   @relation(fields: [fuelTypeId], references: [id])
  FuelTypeRawData               FuelTypeRawData?            @relation(fields: [fuelTypeRawDataId], references: [id])

  @@schema("career")
}

model ExperienceCargo {
  id               String         @id @default(uuid()) @db.Uuid
  experienceShipId String         @db.Uuid
  name             String         @db.VarChar(100)
  description      String?        @db.VarChar(150)
  fromDate         DateTime       @db.Date
  toDate           DateTime?      @db.Date
  createdAt        DateTime       @default(now()) @db.Timestamptz()
  updatedAt        DateTime       @updatedAt @db.Timestamptz()
  ExperienceShip   ExperienceShip @relation(fields: [experienceShipId], references: [id], onDelete: Cascade)

  @@schema("career")
}

//#endregion career
//#region company
model Entity {
  id                            String                          @id @default(uuid()) @db.Uuid
  name                          String                          @db.VarChar(255)
  memberCount                   Int                             @default(0)
  countryIso2                   String?                         @db.Char(2)
  website                       String?                         @db.VarChar(255)
  type                          EntityTypeE
  createdAt                     DateTime                        @default(now()) @db.Timestamp()
  updatedAt                     DateTime                        @updatedAt @db.Timestamp()
  Country                       Country?                        @relation(fields: [countryIso2], references: [iso2])
  Experience                    Experience[]
  ProfileCertificate            ProfileCertificate[]
  ProfileEducation              ProfileEducation[]
  ProfileSkillEntityCertificate ProfileSkillEntityCertificate[]
  ProfileSkillEntityDegree      ProfileSkillEntityDegree[]
  Profile                       Profile[]
  EntitySubTypeEntity           EntitySubTypeEntity[]
  EntityMember                  EntityMember[]
  EntityTab                     EntityTab[]
  EntityProfile                 EntityProfile[]
  Job                           Job[]
  EntityRequest                 EntityRequest[]
  JobApplication                JobApplication[]

  @@index([name])
  @@schema("company")
}

model EntitySubType {
  id                  String                @id @default(uuid()) @db.Uuid
  name                String                @db.VarChar(100)
  type                EntityTypeE
  createdAt           DateTime              @default(now()) @db.Timestamptz()
  updatedAt           DateTime              @updatedAt @db.Timestamptz()
  EntitySubTypeEntity EntitySubTypeEntity[]

  @@schema("company")
}

model EntitySubTypeEntity {
  entityId        String        @db.Uuid
  subEntityTypeId String        @db.Uuid
  createdAt       DateTime      @default(now()) @db.Timestamptz()
  updatedAt       DateTime      @updatedAt @db.Timestamptz()
  Entity          Entity        @relation(fields: [entityId], references: [id])
  EntitySubType   EntitySubType @relation(fields: [subEntityTypeId], references: [id])

  @@unique([entityId, subEntityTypeId])
  @@schema("company")
}

model EntityMember {
  id              String            @id @default(uuid()) @db.Uuid
  entityProfileId String            @db.Uuid
  entityId        String?           @db.Uuid
  entityRawDataId String?           @db.Uuid
  profileId       String            @db.Uuid
  role            EntityMemberRoleE
  createdAt       DateTime          @default(now()) @db.Timestamp()
  updatedAt       DateTime          @updatedAt @db.Timestamp()

  EntityProfile EntityProfile  @relation(fields: [entityProfileId], references: [id])
  Entity        Entity?        @relation(fields: [entityId], references: [id])
  EntityRawData EntityRawData? @relation(fields: [entityRawDataId], references: [id])
  Profile       Profile        @relation(fields: [profileId], references: [id])

  @@index([profileId, entityProfileId, role])
  @@schema("company")
}

model EntityTab {
  id              String  @id @default(uuid()) @db.Uuid
  entityId        String? @db.Uuid
  entityRawDataId String? @db.Uuid

  peopleTab     Boolean @default(false)
  alumniTab     Boolean @default(false)
  jobPostingTab Boolean @default(false)

  createdAt DateTime @default(now()) @db.Timestamp()
  updatedAt DateTime @updatedAt @db.Timestamp()

  Entity        Entity?        @relation(fields: [entityId], references: [id], onDelete: Cascade)
  EntityRawData EntityRawData? @relation(fields: [entityRawDataId], references: [id], onDelete: Cascade)

  @@unique([entityId])
  @@unique([entityRawDataId])
  @@schema("company")
}

model EntityRequest {
  id              String               @id @default(uuid()) @db.Uuid
  entityProfileId String               @db.Uuid
  entityId        String?              @db.Uuid
  entityRawDataId String?              @db.Uuid
  profileId       String               @db.Uuid
  status          EntityRequestStatusE @default(PENDING)
  purpose         String?              @db.VarChar(1000)
  createdAt       DateTime             @default(now()) @db.Timestamp()
  updatedAt       DateTime             @updatedAt @db.Timestamp()

  EntityProfile EntityProfile  @relation(fields: [entityProfileId], references: [id])
  Entity        Entity?        @relation(fields: [entityId], references: [id])
  EntityRawData EntityRawData? @relation(fields: [entityRawDataId], references: [id])
  Profile       Profile        @relation(fields: [profileId], references: [id])

  @@schema("company")
}

model EntityProfile {
  id                              String         @id @default(uuid()) @db.Uuid
  entityId                        String?        @db.Uuid
  entityRawDataId                 String?        @db.Uuid
  profileId                       String         @db.Uuid
  type                            EntityTypeE
  name                            String         @db.VarChar(255)
  avatar                          String?        @db.Text
  website                         String?        @db.VarChar(255)
  email                           String         @db.VarChar(255)
  overview                        String?        @db.VarChar(1000)
  foundedAt                       DateTime?
  description                     String?        @db.VarChar(511)
  isVerified                      Boolean        @default(false)
  emailVerificationFirstAttemptAt DateTime?
  emailVerificationCount          Int            @default(0)
  followersCount                  Int            @default(0)
  followingsCount                 Int            @default(0)
  status                          ProfileStatusE @default(ACTIVE)
  createdAt                       DateTime       @default(now()) @db.Timestamp()
  updatedAt                       DateTime       @updatedAt @db.Timestamp()

  Entity              Entity?               @relation(fields: [entityId], references: [id])
  EntityRawData       EntityRawData?        @relation(fields: [entityRawDataId], references: [id])
  Profile             Profile               @relation(fields: [profileId], references: [id])
  EntityRequest       EntityRequest[]
  EntityMember        EntityMember[]
  Post                Post[]
  PostMedia           PostMedia[]
  PostReaction        PostReaction[]
  PostCommentReaction PostCommentReaction[]
  PostComment         PostComment[]
  Follows             Follow[]              @relation("FollowerEntityProfile")
  Following           Follow[]              @relation("FolloweeEntityProfile")
  Announcement        Announcement[]
  RSVP                RSVP[]
  Job                 Job[]
  Community           Community[]
  CommunityMember     CommunityMember[]
  CommunityRequest    CommunityRequest[]
  Question            Question[]
  Answer              Answer[]
  AnswerVote          AnswerVote[]
  QuestionVote        QuestionVote[]
  AnswerComment       AnswerComment[]
  QuestionComment     QuestionComment[]

  @@unique([entityId, profileId])
  @@unique([entityRawDataId, profileId])
  @@schema("company")
}

enum JobStatus {
  ACTIVE
  DELETED
  EXPIRED
  DRAFT

  @@schema("company")
}

enum JobTypeE {
  SAILING
  NON_SAILING

  @@schema("company")
}

enum RequirementTypeE {
  BASIC
  ADVANCED

  @@schema("company")
}

enum BenefitTypeE {
  BASIC
  ADVANCED

  @@schema("company")
}

enum SalaryTypeE {
  CONTRACTUAL
  ROUND_THE_YEAR
  OTHER

  @@schema("company")
}

enum InsuranceTypeE {
  SELF
  FAMILY
  OTHER

  @@schema("company")
}

enum ItfTypeE {
  ITF
  NON_ITF

  @@schema("company")
}

enum ApplicationMethodE {
  IN_APP
  EMAIL
  EXTERNAL_LINK

  @@schema("company")
}

model Job {
  id                       String  @default(uuid()) @db.Uuid
  cursorId                 BigInt  @default(autoincrement()) @db.BigInt()
  profileId                String  @db.Uuid
  entityProfileId          String? @db.Uuid
  isUrgent                 Boolean @default(false) @db.Boolean
  entityId                 String? @db.Uuid
  entityRawDataId          String? @db.Uuid
  designationAlternativeId String? @db.Uuid
  designationRawDataId     String? @db.Uuid
  departmentAlternativeId  String? @db.Uuid
  departmentRawDataId      String? @db.Uuid
  shipImo                  String? @db.VarChar(7)
  shipRawDataImo           String? @db.VarChar(7)
  shipTypeId               String? @db.Uuid
  shipTypeRawDataId        String? @db.Uuid
  countryIso2              String? @db.Char(2)

  minYears   Int?
  maxYears   Int?
  minSalary  Int?
  maxSalary  Int?
  status     JobStatus @default(DRAFT)
  expiryDate DateTime
  isOfficial Boolean   @default(false)
  createdAt  DateTime  @default(now()) @db.Timestamp()
  updatedAt  DateTime  @updatedAt @db.Timestamp()

  jobType               JobTypeE?               @default(SAILING)
  genderDiversityIndex  Decimal?                @db.Decimal(2, 1)
  about                 String?                 @db.Text
  rolesResponsibilities String?                 @db.Text
  requirementType       RequirementTypeE?       @default(BASIC)
  requirements          String?                 @db.Text
  benefits              String?                 @db.Text
  salaryType            SalaryTypeE?
  currencyCode          String?                 @db.VarChar(3)
  showSalary            Boolean                 @default(true)
  showShipDetails       Boolean                 @default(true)
  applicationMethod     ApplicationMethodE      @default(IN_APP)
  applicationEmail      String?                 @db.VarChar(255)
  applicationUrl        String?                 @db.Text
  joiningDate           DateTime?               @db.Date()

  Profile                  Profile                    @relation(fields: [profileId], references: [id])
  EntityProfile            EntityProfile?             @relation(fields: [entityProfileId], references: [id])
  DesignationAlternative   DesignationAlternative?    @relation(fields: [designationAlternativeId], references: [id])
  DesignationRawData       DesignationRawData?        @relation(fields: [designationRawDataId], references: [id])
  DepartmentAlternative    DepartmentAlternative?     @relation(fields: [departmentAlternativeId], references: [id], onDelete: Cascade)
  DepartmentRawData        DepartmentRawData?         @relation(fields: [departmentRawDataId], references: [id], onDelete: Cascade)
  Ship                     Ship?                      @relation(fields: [shipImo], references: [imo])
  ShipRawData              ShipRawData?               @relation(fields: [shipRawDataImo], references: [imo])
  ShipType                 SubVesselType?             @relation(fields: [shipTypeId], references: [id])
  ShipTypeRawData          SubVesselTypeRawData?      @relation(fields: [shipTypeRawDataId], references: [id])
  Entity                   Entity?                    @relation(fields: [entityId], references: [id])
  EntityRawData            EntityRawData?             @relation(fields: [entityRawDataId], references: [id])
  Country                   Country?  @relation(fields: [countryIso2], references: [iso2])
  JobEquipmentRequirement  JobEquipmentRequirement[]
  JobEntityBenefit         JobEntityBenefit[]
  JobApplication           JobApplication[]
  JobCertificationRequirement  JobCertificationRequirement[]
  JobDocumentRequirement       JobDocumentRequirement[]
  JobExperienceRequirement     JobExperienceRequirement[]
  JobSkillRequirement          JobSkillRequirement[]
  JobCargoRequirement          JobCargoRequirement[]
  JobOtherRequirement          JobOtherRequirement[]
  JobBenefit                   JobBenefit?

  @@id([id])
  @@schema("company")
}

model JobDocumentRequirement {
    id                    String  @id @default(uuid()) @db.Uuid
    jobId                 String  @db.Uuid
    documentTypeId        String? @db.Uuid
    documentTypeRawDataId String? @db.Uuid
    countries             String[]
    isMandatory           Boolean @default(false)
    description           String? @db.VarChar(500)
    sequence              Int     @default(0)
    createdAt             DateTime @default(now()) @db.Timestamp()
    updatedAt             DateTime @updatedAt @db.Timestamp()

    Job                   Job                  @relation(fields:[jobId], references: [id], onDelete: Cascade)
    DocumentType          DocumentType?        @relation(fields:[documentTypeId], references: [id])
    DocumentTypeRawData   DocumentTypeRawData? @relation(fields:[documentTypeRawDataId], references: [id])

    @@schema("company")
    @@map("JobDocumentRequirement")
}

model JobCertificationRequirement {
  id                          String  @default(uuid()) @db.Uuid
  jobId                       String  @db.Uuid
  certificateCourseId         String? @db.Uuid
  certificateCourseRawDataId  String? @db.Uuid
  isMandatory                 Boolean @default(false)
  createdAt                   DateTime @default(now()) @db.Timestamp()
  updatedAt                   DateTime @updatedAt @db.Timestamp()

  CertificateCourse        CertificateCourse?        @relation(fields: [certificateCourseId], references: [id])
  CertificateCourseRawData CertificateCourseRawData? @relation(fields: [certificateCourseRawDataId], references: [id])
  Job                      Job                       @relation(fields: [jobId], references: [id])

  @@id([id])
  @@schema("company")
}

model JobExperienceRequirement {
  id                       String   @default(uuid()) @db.Uuid
  jobId                    String   @db.Uuid
  designationAlternativeId String?  @db.Uuid
  designationRawDataId     String?  @db.Uuid
  subVesselTypeId         String?  @db.Uuid
  subVesselTypeRawDataId  String?  @db.Uuid
  monthsOfExperience       Int
  isMandatory              Boolean  @default(false)
  isTotal                  Boolean  @default(false)
  createdAt                DateTime @default(now()) @db.Timestamp()
  updatedAt                DateTime @updatedAt @db.Timestamp()

  Job                    Job                     @relation(fields: [jobId], references: [id])
  DesignationAlternative DesignationAlternative? @relation(fields: [designationAlternativeId], references: [id])
  DesignationRawData     DesignationRawData?     @relation(fields: [designationRawDataId], references: [id])
  SubVesselType         SubVesselType?         @relation(fields: [subVesselTypeId], references: [id])
  SubVesselTypeRawData  SubVesselTypeRawData?  @relation(fields: [subVesselTypeRawDataId], references: [id])

  @@id([id])
  @@schema("company")
}

model JobSkillRequirement {
  id             String   @default(uuid()) @db.Uuid
  jobId          String   @db.Uuid
  skillId        String?  @db.Uuid
  skillRawDataId String?  @db.Uuid
  isMandatory    Boolean  @default(false)
  createdAt      DateTime @default(now()) @db.Timestamp()
  updatedAt      DateTime @updatedAt @db.Timestamp()

  Job          Job           @relation(fields: [jobId], references: [id])
  Skill        Skill?        @relation(fields: [skillId], references: [id])
  SkillRawData SkillRawData? @relation(fields: [skillRawDataId], references: [id])

  @@id([id])
  @@schema("company")
}

model JobCargoRequirement {
  id                 String   @default(uuid()) @db.Uuid
  jobId              String   @db.Uuid
  name               String   @db.VarChar(100)
  code               String?  @db.VarChar(50)
  monthsOfExperience Int
  isMandatory        Boolean  @default(false)
  createdAt          DateTime @default(now()) @db.Timestamp()
  updatedAt          DateTime @updatedAt @db.Timestamp()

  Job Job @relation(fields: [jobId], references: [id])

  @@id([id])
  @@schema("company")
}

model JobOtherRequirement {
  id             String   @default(uuid()) @db.Uuid
  jobId          String   @db.Uuid
  sequenceNumber Int
  details        String   @db.Text
  isMandatory    Boolean  @default(false)
  createdAt      DateTime @default(now()) @db.Timestamp()
  updatedAt      DateTime @updatedAt @db.Timestamp()

  Job Job @relation(fields: [jobId], references: [id])

  @@id([id])
  @@schema("company")
}

model JobBenefitDetail {
  id             String   @default(uuid()) @db.Uuid
  jobBenefitId   String   @db.Uuid
  sequenceNumber Int
  details        String   @db.Text
  createdAt      DateTime @default(now()) @db.Timestamp()
  updatedAt      DateTime @updatedAt @db.Timestamp()

  JobBenefit JobBenefit @relation(fields: [jobBenefitId], references: [id])

  @@id([id])
  @@schema("company")
}

model JobBenefit {
  id    String @default(uuid()) @db.Uuid
  jobId String @unique @db.Uuid

  // Contract duration details
  contractMonths Int?
  contractDays   Int?

  // Internet details
  internetAvailable   Boolean?
  internetSpeed       Int?
  internetLimitPerDay Int?
  internetDetails     String?  @db.VarChar(500)

  // Insurance details
  insuranceType InsuranceTypeE?
  familyOnboard Boolean?

  // Other benefit details
  benefitType BenefitTypeE? @default(BASIC)
  itfType     ItfTypeE?

  createdAt DateTime @default(now()) @db.Timestamp()
  updatedAt DateTime @updatedAt @db.Timestamp()

  Job              Job                @relation(fields: [jobId], references: [id])
  JobBenefitDetail JobBenefitDetail[]

  @@id([id])
  @@schema("company")
}

model JobEquipmentRequirement {
  id                             String   @default(uuid()) @db.Uuid
  jobId                          String   @db.Uuid
  sequenceNumber                 Int
  equipmentCategoryId            String?  @db.Uuid
  equipmentCategoryRawDataId     String?  @db.Uuid
  equipmentManufacturerId        String?  @db.Uuid
  equipmentManufacturerRawDataId String?  @db.Uuid
  equipmentModelId               String?  @db.Uuid
  equipmentModelRawDataId        String?  @db.Uuid
  fuelTypeId                     String?  @db.Uuid
  fuelTypeRawDataId              String?  @db.Uuid
  monthsOfExperience             Int
  isMandatory                    Boolean  @default(false)
  createdAt                      DateTime @default(now()) @db.Timestamp()
  updatedAt                      DateTime @updatedAt @db.Timestamp()

  Job                          Job                           @relation(fields: [jobId], references: [id])
  EquipmentCategory            EquipmentCategory?            @relation(fields: [equipmentCategoryId], references: [id])
  EquipmentCategoryRawData     EquipmentCategoryRawData?     @relation(fields: [equipmentCategoryRawDataId], references: [id])
  EquipmentManufacturer        EquipmentManufacturer?        @relation(fields: [equipmentManufacturerId], references: [id])
  EquipmentManufacturerRawData EquipmentManufacturerRawData? @relation(fields: [equipmentManufacturerRawDataId], references: [id])
  EquipmentModel               EquipmentModel?               @relation(fields: [equipmentModelId], references: [id])
  EquipmentModelRawData        EquipmentModelRawData?        @relation(fields: [equipmentModelRawDataId], references: [id])
  FuelType                     FuelType?                     @relation(fields: [fuelTypeId], references: [id])
  FuelTypeRawData              FuelTypeRawData?              @relation(fields: [fuelTypeRawDataId], references: [id])

  @@id([id])
  @@schema("company")
}

model JobEntityBenefit {
  id                     String  @default(uuid()) @db.Uuid
  jobId                  String  @db.Uuid
  entityBenefitRawDataId String? @db.Uuid

  createdAt DateTime @default(now()) @db.Timestamp()
  updatedAt DateTime @updatedAt @db.Timestamp()

  EquipmentCategoryRawData EntityBenefitRawData? @relation(fields: [entityBenefitRawDataId], references: [id])
  Job                      Job                   @relation(fields: [jobId], references: [id])

  @@id([id])
  @@schema("company")
}

enum ApplicationStatus {
  PENDING
  WITHDREW
  SHORTLISTED
  REJECTED_BY_ENTITY
  OFFERED
  ACCEPTED_BY_APPLICANT
  REJECTED_BY_APPLICANT

  @@schema("company")
}

model JobApplication {
  id                     String            @default(uuid()) @db.Uuid
  cursorId               BigInt            @default(autoincrement()) @db.BigInt()
  jobId                  String            @db.Uuid
  applicantId            String            @db.Uuid
  entityId               String?           @db.Uuid
  entityRawDataId        String?           @db.Uuid
  decisionMakerProfileId String?           @db.Uuid
  matching               Int
  status                 ApplicationStatus @default(PENDING)
  applicantCount         Int               @default(0)
  createdAt              DateTime          @default(now()) @db.Timestamp()
  updatedAt              DateTime          @updatedAt @db.Timestamp()

  ApplicantProfile     Profile        @relation("ApplicantProfile", fields: [applicantId], references: [id])
  DecisionMakerProfile Profile?       @relation("DecisionMakerProfile", fields: [decisionMakerProfileId], references: [id])
  Job                  Job            @relation(fields: [jobId], references: [id])
  Entity               Entity?        @relation(fields: [entityId], references: [id])
  EntityRawData        EntityRawData? @relation(fields: [entityRawDataId], references: [id])

  @@id([id])
  @@unique([applicantId, jobId])
  @@schema("company")
}

model Department {
  id                    String                  @id @default(uuid()) @db.Uuid
  name                  String                  @db.VarChar(100)
  createdAt             DateTime                @default(now()) @db.Timestamp()
  updatedAt             DateTime                @updatedAt @db.Timestamp()
  DepartmentAlternative DepartmentAlternative[]

  @@schema("company")
}

model DepartmentAlternative {
  id             String           @id @default(uuid()) @db.Uuid
  name           String           @db.VarChar(100)
  departmentId   String           @db.Uuid
  createdAt      DateTime         @default(now()) @db.Timestamp()
  updatedAt      DateTime         @updatedAt @db.Timestamp()
  ExperienceShip ExperienceShip[]
  Department     Department       @relation(fields: [departmentId], references: [id])
  Question       Question[]
  Job            Job[]

  @@schema("company")
}

model Designation {
  id                     String                   @id @default(uuid()) @db.Uuid
  name                   String                   @db.VarChar(100)
  createdAt              DateTime                 @default(now()) @db.Timestamp()
  updatedAt              DateTime                 @updatedAt @db.Timestamp()
  DesignationAlternative DesignationAlternative[]

  @@schema("company")
}

model DesignationAlternative {
  id                       String                     @id @default(uuid()) @db.Uuid
  name                     String                     @db.VarChar(100)
  designationId            String                     @db.Uuid
  createdAt                DateTime                   @default(now()) @db.Timestamp()
  updatedAt                DateTime                   @updatedAt @db.Timestamp()
  ExperienceDesignation    ExperienceDesignation[]
  Designation              Designation                @relation(fields: [designationId], references: [id], onDelete: Cascade)
  Profile                  Profile[]
  Job                      Job[]
  JobExperienceRequirement JobExperienceRequirement[]

  @@schema("company")
}

model Skill {
  id                            String                          @id @default(uuid()) @db.Uuid
  name                          String                          @db.VarChar(100)
  category                      SkillCategoryE
  createdAt                     DateTime                        @default(now()) @db.Timestamp()
  updatedAt                     DateTime                        @updatedAt @db.Timestamp()
  ProfileSkill                  ProfileSkill[]
  ProfileSkillEntityCertificate ProfileSkillEntityCertificate[]
  ProfileSkillEntityDegree      ProfileSkillEntityDegree[]
  ProfileSkillExperienceShip    ProfileSkillExperienceShip[]
  JobSkillRequirement           JobSkillRequirement[]

  @@schema("company")
}

model Degree {
  id                       String                     @id @default(uuid()) @db.Uuid
  name                     String                     @db.VarChar(150)
  createdAt                DateTime                   @default(now()) @db.Timestamptz()
  updatedAt                DateTime                   @updatedAt @db.Timestamptz()
  ProfileEducation         ProfileEducation[]
  ProfileSkillEntityDegree ProfileSkillEntityDegree[]

  @@schema("company")
}

model CertificateCourse {
  id                          String                        @id @default(uuid()) @db.Uuid
  name                        String                        @db.VarChar(255)
  type                        CertificateCourseTypeE
  createdAt                   DateTime                      @default(now()) @db.Timestamptz()
  updatedAt                   DateTime                      @updatedAt @db.Timestamptz()
  ProfileCertificate          ProfileCertificate[]
  JobCertificationRequirement JobCertificationRequirement[]

  @@schema("company")
}

//#endregion company

//#region document


enum DocumentCategoryE {
  IDENTITY
  VISA

  @@schema("document")
}

model DocumentType {
  id           String            @id @default(uuid()) @db.Uuid
  name         String            @db.VarChar(150)
  category     DocumentCategoryE
  createdAt    DateTime          @default(now()) @db.Timestamp()
  updatedAt    DateTime          @updatedAt @db.Timestamp()

  Identity     Identity[]
  Visa         Visa[]
  JobDocumentRequirement  JobDocumentRequirement[]

  @@unique([name, category])
  @@index([category])
  @@schema("document")
}

model Identity {
  id                    String              @id @default(uuid()) @db.Uuid
  documentNo            String              @db.VarChar(50)
  profileId             String              @db.Uuid
  fileUrl               String?             @db.Text
  documentTypeId        String?             @db.Uuid
  documentTypeRawDataId String?             @db.Uuid

  type                  IdentityTypeE
  orderIndex            Int
  countryIso2           String                @db.Char(2)
  fromDate              DateTime              @db.Date
  untilDate             DateTime?             @db.Date
  createdAt             DateTime              @default(now()) @db.Timestamp()
  updatedAt             DateTime              @updatedAt @db.Timestamp()

  Country               Country              @relation(fields: [countryIso2], references: [iso2])
  Profile               Profile              @relation(fields: [profileId], references: [id])
  DocumentType          DocumentType?        @relation(fields: [documentTypeId], references: [id])
  DocumentTypeRawData   DocumentTypeRawData? @relation(fields: [documentTypeRawDataId], references: [id])

  @@index([profileId, documentTypeId])
  @@index([profileId, documentTypeRawDataId])
  @@index([profileId, documentNo])
  @@schema("document")
}

model Visa {
  id                    String    @id @default(uuid()) @db.Uuid
  documentNo            String    @db.VarChar(50)
  name                  String?   @db.VarChar(150)
  documentTypeId        String?   @db.Uuid
  documentTypeRawDataId String?   @db.Uuid
  profileId             String    @db.Uuid
  fileUrl               String?   @db.Text
  countryIso2           String    @db.Char(2)
  fromDate              DateTime  @db.Date
  untilDate             DateTime? @db.Date
  createdAt             DateTime  @default(now()) @db.Timestamp()
  updatedAt             DateTime  @updatedAt @db.Timestamp()
  Country               Country   @relation(fields: [countryIso2], references: [iso2])
  profile               Profile   @relation(fields: [profileId], references: [id])

  DocumentType        DocumentType?        @relation(fields: [documentTypeId], references: [id])
  DocumentTypeRawData DocumentTypeRawData? @relation(fields: [documentTypeRawDataId], references: [id])

  @@index([profileId, documentTypeId])
  @@index([profileId, documentTypeRawDataId])
  @@unique([profileId, documentNo])
  @@schema("document")
}

//#endregion document

//#region feed
model Post {
  id                  String         @id @default(uuid()) @db.Uuid
  cursorId            BigInt         @default(autoincrement()) @db.BigInt()
  caption             String?        @db.VarChar(2000)
  reactionsCount      Int            @default(0)
  parentCommentsCount Int            @default(0)
  totalCommentsCount  Int            @default(0)
  profileId           String         @db.Uuid
  entityProfileId     String?        @db.Uuid
  status              PostStatusE    @default(ACTIVE)
  createdAt           DateTime       @default(now()) @db.Timestamp()
  updatedAt           DateTime       @updatedAt @db.Timestamp()
  Profile             Profile        @relation(fields: [profileId], references: [id])
  EntityProfile       EntityProfile? @relation(fields: [entityProfileId], references: [id])
  Comments            PostComment[]
  Media               PostMedia[]
  Reactions           PostReaction[]
  SavedPost           SavedPost[]

  @@index([profileId])
  @@index([entityProfileId])
  @@index([cursorId])
  @@schema("feed")
}

enum PostFileExtensionE {
  webp
  jpeg
  jpg
  mp4

  @@schema("feed")
}

model PostMedia {
  id              String             @id @default(uuid()) @db.Uuid
  postId          String             @db.Uuid
  profileId       String?            @db.Uuid
  entityProfileId String?            @db.Uuid
  caption         String?            @db.VarChar(1000)
  fileUrl         String             @db.Text
  fileExtension   PostFileExtensionE
  createdAt       DateTime           @default(now()) @db.Timestamp()
  updatedAt       DateTime           @updatedAt @db.Timestamp()
  Post            Post               @relation(fields: [postId], references: [id], onDelete: Cascade)
  Profile         Profile?           @relation(fields: [profileId], references: [id], onDelete: Cascade)
  EntityProfile   EntityProfile?     @relation(fields: [entityProfileId], references: [id], onDelete: Cascade)

  @@index([postId])
  @@index([profileId])
  @@index([entityProfileId])
  @@schema("feed")
}

model PostReaction {
  id              String         @default(uuid()) @db.Uuid
  profileId       String?        @db.Uuid
  entityProfileId String?        @db.Uuid
  postId          String         @db.Uuid
  reactionType    ReactionTypeE  @default(LIKE)
  createdAt       DateTime       @default(now()) @db.Timestamp()
  Post            Post           @relation(fields: [postId], references: [id], onDelete: Cascade)
  Profile         Profile?       @relation(fields: [profileId], references: [id], onDelete: Cascade)
  EntityProfile   EntityProfile? @relation(fields: [entityProfileId], references: [id], onDelete: Cascade)

  @@id(id)
  @@index([postId])
  @@schema("feed")
}

model PostComment {
  id              String                @default(uuid()) @db.Uuid
  cursorId        BigInt                @default(autoincrement()) @db.BigInt()
  text            String                @db.VarChar(255)
  profileId       String?               @db.Uuid
  entityProfileId String?               @db.Uuid
  postId          String                @db.Uuid
  status          PostStatusE           @default(ACTIVE)
  repliesCount    Int?
  reactionsCount  Int                   @default(0)
  parentCommentId String?               @db.Uuid
  createdAt       DateTime              @default(now()) @db.Timestamp()
  updatedAt       DateTime              @updatedAt @db.Timestamp()
  Parent          PostComment?          @relation("CommentReplies", fields: [parentCommentId], references: [id], onDelete: Cascade)
  Replies         PostComment[]         @relation("CommentReplies")
  Post            Post                  @relation(fields: [postId], references: [id], onDelete: Cascade)
  Profile         Profile?              @relation(fields: [profileId], references: [id], onDelete: Cascade)
  EntityProfile   EntityProfile?        @relation(fields: [entityProfileId], references: [id], onDelete: Cascade)
  Reactions       PostCommentReaction[]

  @@id(id)
  @@index([postId])
  @@index([profileId])
  @@index([parentCommentId])
  @@schema("feed")
}

model PostCommentReaction {
  id              String         @default(uuid()) @db.Uuid
  profileId       String         @db.Uuid
  entityProfileId String?        @db.Uuid
  commentId       String         @db.Uuid
  reactionType    ReactionTypeE  @default(LIKE)
  createdAt       DateTime       @default(now()) @db.Timestamp()
  Comment         PostComment    @relation(fields: [commentId], references: [id], onDelete: Cascade)
  Profile         Profile        @relation(fields: [profileId], references: [id], onDelete: Cascade)
  EntityProfile   EntityProfile? @relation(fields: [entityProfileId], references: [id], onDelete: Cascade)

  @@id(id)
  @@index([commentId])
  @@schema("feed")
}

//#endregion feed

//#region forum
model Community {
  id                     String           @default(uuid()) @db.Uuid
  cursorId               BigInt           @default(autoincrement()) @db.BigInt()
  name                   String           @db.VarChar(100)
  description            String?          @db.VarChar(300)
  memberCount            Int              @default(0)
  questionCount          Int              @default(0)
  access                 CommunityAccessE
  creatorId              String?          @db.Uuid
  creatorEntityProfileId String?          @db.Uuid
  isRestricted           Boolean          @default(false)
  createdAt              DateTime         @default(now()) @db.Timestamptz()
  updatedAt              DateTime         @updatedAt @db.Timestamptz()
  avatar                 String?          @db.Text

  CommunityMember  CommunityMember[]
  Profile          Profile?           @relation(fields: [creatorId], references: [id])
  EntityProfile    EntityProfile?     @relation(fields: [creatorEntityProfileId], references: [id])
  Question         Question[]
  Answer           Answer[]
  CommunityRequest CommunityRequest[]
  AnswerVote       AnswerVote[]
  QuestionVote     QuestionVote[]
  AnswerComment    AnswerComment[]
  QuestionMedia    QuestionMedia[]
  AnswerMedia      AnswerMedia[]

  @@id([id])
  @@schema("forum")
}

model CommunityMember {
  id              String      @default(uuid()) @db.Uuid
  communityId     String      @db.Uuid
  profileId       String?     @db.Uuid
  entityProfileId String?     @db.Uuid
  type            MemberTypeE @default(MEMBER)
  name            String?     @db.VarChar(100)
  createdAt       DateTime    @default(now()) @db.Timestamptz()
  updatedAt       DateTime    @updatedAt @db.Timestamptz()

  Community     Community      @relation(fields: [communityId], references: [id], onDelete: Cascade)
  Profile       Profile?       @relation(fields: [profileId], references: [id], onDelete: Cascade)
  EntityProfile EntityProfile? @relation(fields: [entityProfileId], references: [id])

  @@id(id)
  @@schema("forum")
}

model CommunityRequest {
  id              String                  @default(uuid()) @db.Uuid
  communityId     String                  @db.Uuid
  profileId       String?                 @db.Uuid
  entityProfileId String?                 @db.Uuid
  requestedType   MemberTypeE
  acceptedType    MemberTypeE?
  status          CommunityRequestStatusE @default(PENDING)
  createdAt       DateTime                @default(now()) @db.Timestamptz()
  updatedAt       DateTime                @updatedAt @db.Timestamptz()

  Community     Community      @relation(fields: [communityId], references: [id], onDelete: Cascade)
  Profile       Profile?       @relation(fields: [profileId], references: [id], onDelete: Cascade)
  EntityProfile EntityProfile? @relation(fields: [entityProfileId], references: [id])

  @@id(id)
  @@schema("forum")
}

model Question {
  id                             String        @default(uuid()) @db.Uuid
  cursorId                       BigInt        @default(autoincrement()) @db.BigInt()
  slug                           String?       @unique @db.VarChar(255)
  title                          String        @db.VarChar(150)
  description                    String        @db.VarChar(2000)
  type                           QuestionTypeE @default(NORMAL)
  communityId                    String        @db.Uuid
  profileId                      String?       @db.Uuid
  entityProfileId                String?       @db.Uuid
  upvoteCount                    Int           @default(0)
  downvoteCount                  Int           @default(0)
  answerCount                    Int           @default(0)
  commentCount                   Int           @default(0)
  isLive                         Boolean
  liveStartedAt                  DateTime?     @db.Timestamptz()
  shipImo                        String?       @db.VarChar(7)
  shipRawDataImo                 String?       @db.VarChar(7)
  mainVesselTypeId               String?       @db.Uuid
  mainVesselTypeRawDataId        String?       @db.Uuid
  subVesselTypeId                String?       @db.Uuid
  subVesselTypeRawDataId         String?       @db.Uuid
  isSolved                       Boolean       @default(false)
  isEdited                       Boolean       @default(false)
  isAnonymous                    Boolean       @default(false)
  equipmentCategoryId            String?       @db.Uuid
  equipmentCategoryRawDataId     String?       @db.Uuid
  equipmentModelId               String?       @db.Uuid
  equipmentModelRawDataId        String?       @db.Uuid
  equipmentManufacturerId        String?       @db.Uuid
  equipmentManufacturerRawDataId String?       @db.Uuid
  departmentAlternativeId        String?       @db.Uuid
  departmentRawDataId            String?       @db.Uuid
  createdAt                      DateTime      @default(now()) @db.Timestamptz()
  updatedAt                      DateTime      @updatedAt @db.Timestamptz()

  Community                    Community                     @relation(fields: [communityId], references: [id], onDelete: Cascade)
  Profile                      Profile?                      @relation(fields: [profileId], references: [id], onDelete: Cascade)
  EntityProfile                EntityProfile?                @relation(fields: [entityProfileId], references: [id])
  EquipmentCategory            EquipmentCategory?            @relation(fields: [equipmentCategoryId], references: [id])
  EquipmentCategoryRawData     EquipmentCategoryRawData?     @relation(fields: [equipmentCategoryRawDataId], references: [id])
  EquipmentModel               EquipmentModel?               @relation(fields: [equipmentModelId], references: [id])
  EquipmentModelRawData        EquipmentModelRawData?        @relation(fields: [equipmentModelRawDataId], references: [id])
  EquipmentManufacturer        EquipmentManufacturer?        @relation(fields: [equipmentManufacturerId], references: [id])
  EquipmentManufacturerRawData EquipmentManufacturerRawData? @relation(fields: [equipmentManufacturerRawDataId], references: [id])

  Ship        Ship?        @relation(fields: [shipImo], references: [imo])
  ShipRawData ShipRawData? @relation(fields: [shipRawDataImo], references: [imo])

  MainVesselType        MainVesselType?        @relation(fields: [mainVesselTypeId], references: [id])
  MainVesselTypeRawData MainVesselTypeRawData? @relation(fields: [mainVesselTypeRawDataId], references: [id])

  SubVesselType        SubVesselType?        @relation(fields: [subVesselTypeId], references: [id])
  SubVesselTypeRawData SubVesselTypeRawData? @relation(fields: [subVesselTypeRawDataId], references: [id])

  DepartmentAlternative DepartmentAlternative? @relation(fields: [departmentAlternativeId], references: [id], onDelete: Cascade)
  DepartmentRawData     DepartmentRawData?     @relation(fields: [departmentRawDataId], references: [id], onDelete: Cascade)

  Answer          Answer[]
  QuestionVote    QuestionVote[]
  QuestionMedia   QuestionMedia[]
  QuestionTopic   QuestionTopic[]
  QuestionComment QuestionComment[]
  AnswerVote      AnswerVote[]
  AnswerComment   AnswerComment[]

  @@id([id])
  @@schema("forum")
}

model QuestionMedia {
  id          String @default(uuid()) @db.Uuid
  questionId  String @db.Uuid
  fileUrl     String @db.Text
  communityId String @db.Uuid

  fileExtension ForumFileExtensionE
  Community     Community           @relation(fields: [communityId], references: [id], onDelete: Cascade)
  Question      Question            @relation(fields: [questionId], references: [id], onDelete: Cascade)

  @@id([id])
  @@schema("forum")
}

model Answer {
  id              String        @default(uuid()) @db.Uuid
  slug            String?       @unique @db.VarChar(255)
  cursorId        BigInt        @default(autoincrement()) @db.BigInt()
  text            String        @db.VarChar(2000)
  upvoteCount     Int           @default(0)
  downvoteCount   Int           @default(0)
  commentCount    Int           @default(0)
  questionId      String        @db.Uuid
  communityId     String        @db.Uuid
  profileId       String?       @db.Uuid
  entityProfileId String?       @db.Uuid
  status          AnswerStatusE @default(PROPOSED_SOLUTION)
  isEdited        Boolean       @default(false)
  validatedBy     String?       @db.Uuid
  createdAt       DateTime      @default(now()) @db.Timestamptz()
  updatedAt       DateTime      @updatedAt @db.Timestamptz()

  Community     Community      @relation(fields: [communityId], references: [id], onDelete: Cascade)
  Profile       Profile?       @relation(fields: [profileId], references: [id], onDelete: Cascade)
  EntityProfile EntityProfile? @relation(fields: [entityProfileId], references: [id])
  ValidatedBy   Profile?       @relation("ValidatedBy", fields: [validatedBy], references: [id], onDelete: Cascade)
  Question      Question       @relation(fields: [questionId], references: [id], onDelete: Cascade)

  Vote          AnswerVote[]
  AnswerComment AnswerComment[]
  AnswerMedia   AnswerMedia[]

  @@id([id])
  @@schema("forum")
}

model AnswerMedia {
  id          String @default(uuid()) @db.Uuid
  answerId    String @db.Uuid
  fileUrl     String @db.Text
  communityId String @db.Uuid

  fileExtension ForumFileExtensionE
  Community     Community           @relation(fields: [communityId], references: [id], onDelete: Cascade)
  Answer        Answer              @relation(fields: [answerId], references: [id], onDelete: Cascade)

  @@id([id])
  @@schema("forum")
}

model AnswerVote {
  id              String    @default(uuid()) @db.Uuid
  cursorId        BigInt    @default(autoincrement()) @db.BigInt()
  answerId        String    @db.Uuid
  communityId     String    @db.Uuid
  questionId      String    @db.Uuid
  profileId       String?   @db.Uuid
  entityProfileId String?   @db.Uuid
  type            VoteTypeE
  createdAt       DateTime  @default(now()) @db.Timestamptz()
  updatedAt       DateTime  @updatedAt @db.Timestamptz()

  Answer        Answer         @relation(fields: [answerId], references: [id], onDelete: Cascade)
  Community     Community      @relation(fields: [communityId], references: [id], onDelete: Cascade)
  Profile       Profile?       @relation(fields: [profileId], references: [id], onDelete: Cascade)
  EntityProfile EntityProfile? @relation(fields: [entityProfileId], references: [id])
  Question      Question       @relation(fields: [questionId], references: [id], onDelete: Cascade)

  @@id([id])
  @@schema("forum")
}

model QuestionVote {
  id              String    @default(uuid()) @db.Uuid
  cursorId        BigInt    @default(autoincrement()) @db.BigInt()
  questionId      String    @db.Uuid
  communityId     String    @db.Uuid
  profileId       String?   @db.Uuid
  entityProfileId String?   @db.Uuid
  type            VoteTypeE
  createdAt       DateTime  @default(now()) @db.Timestamptz()
  updatedAt       DateTime  @updatedAt @db.Timestamptz()

  Question      Question       @relation(fields: [questionId], references: [id], onDelete: Cascade)
  Community     Community      @relation(fields: [communityId], references: [id], onDelete: Cascade)
  Profile       Profile?       @relation(fields: [profileId], references: [id], onDelete: Cascade)
  EntityProfile EntityProfile? @relation(fields: [entityProfileId], references: [id])

  @@id([id])
  @@schema("forum")
}

model QuestionComment {
  id              String            @default(uuid()) @db.Uuid
  cursorId        BigInt            @default(autoincrement()) @db.BigInt()
  questionId      String            @db.Uuid
  profileId       String?           @db.Uuid
  entityProfileId String?           @db.Uuid
  text            String            @db.VarChar(255)
  parentCommentId String?           @db.Uuid
  replyCount      Int?
  isAnonymous     Boolean           @default(false)
  createdAt       DateTime          @default(now()) @db.Timestamp()
  updatedAt       DateTime          @updatedAt @db.Timestamptz()
  Question        Question          @relation(fields: [questionId], references: [id], onDelete: Cascade)
  Profile         Profile?          @relation(fields: [profileId], references: [id], onDelete: Cascade)
  EntityProfile   EntityProfile?    @relation(fields: [entityProfileId], references: [id])
  Parent          QuestionComment?  @relation("CommentReplies", fields: [parentCommentId], references: [id], onDelete: Cascade)
  replies         QuestionComment[] @relation("CommentReplies")

  @@id([id])
  @@schema("forum")
}

model AnswerComment {
  id              String   @default(uuid()) @db.Uuid
  cursorId        BigInt   @default(autoincrement()) @db.BigInt()
  communityId     String   @db.Uuid
  questionId      String   @db.Uuid
  answerId        String   @db.Uuid
  parentCommentId String?  @db.Uuid
  profileId       String?  @db.Uuid
  entityProfileId String?  @db.Uuid
  replyCount      Int?
  text            String   @db.VarChar(255)
  createdAt       DateTime @default(now()) @db.Timestamptz()
  updatedAt       DateTime @updatedAt @db.Timestamptz()

  Answer        Answer         @relation(fields: [answerId], references: [id], onDelete: Cascade)
  Community     Community      @relation(fields: [communityId], references: [id], onDelete: Cascade)
  Profile       Profile?       @relation(fields: [profileId], references: [id], onDelete: Cascade)
  EntityProfile EntityProfile? @relation(fields: [entityProfileId], references: [id])
  Question      Question       @relation(fields: [questionId], references: [id], onDelete: Cascade)

  Parent AnswerComment? @relation("CommentReplies", fields: [parentCommentId], references: [id], onDelete: Cascade)

  Replies AnswerComment[] @relation("CommentReplies")

  @@id([id])
  @@schema("forum")
}

model Topic {
  id            String          @default(uuid()) @db.Uuid
  name          String          @db.VarChar(100)
  count         Int             @default(0)
  createdAt     DateTime        @default(now()) @db.Timestamptz()
  updatedAt     DateTime        @updatedAt @db.Timestamptz()
  QuestionTopic QuestionTopic[]

  @@id([id])
  @@schema("forum")
}

model QuestionTopic {
  id             String   @default(uuid()) @db.Uuid
  cursorId       BigInt   @default(autoincrement()) @db.BigInt()
  communityId    String   @db.Uuid
  questionId     String   @db.Uuid
  topicId        String?  @db.Uuid
  topicRawDataId String?  @db.Uuid
  createdAt      DateTime @default(now()) @db.Timestamptz()
  updatedAt      DateTime @updatedAt @db.Timestamptz()

  Question     Question      @relation(fields: [questionId], references: [id], onDelete: Cascade)
  Topic        Topic?        @relation(fields: [topicId], references: [id], onDelete: Cascade)
  TopicRawData TopicRawData? @relation(fields: [topicRawDataId], references: [id], onDelete: Cascade)

  @@id([id])
  @@schema("forum")
}

enum CommunityAccessE {
  GLOBAL
  PUBLIC
  PRIVATE

  @@schema("forum")
}

enum ForumFileExtensionE {
  webp
  jpeg
  jpg
  pdf
  xls
  xlsx
  mp4

  @@schema("forum")
}

enum AnswerStatusE {
  PROPOSED_SOLUTION
  VERIFIED_SOLUTION

  @@schema("forum")
}

enum VoteTypeE {
  UPVOTE
  DOWNVOTE

  @@schema("forum")
}

enum QuestionTypeE {
  NORMAL
  TROUBLESHOOT

  @@schema("forum")
}

enum MemberTypeE {
  ADMIN
  MODERATOR
  CONTRIBUTOR
  MEMBER

  @@schema("forum")
}

enum CommunityRequestStatusE {
  PENDING
  ACCEPTED
  PARTIALLY_ACCEPTED
  REJECTED
  REVOKED

  @@schema("forum")
}

//#endregion forum
//#region master
model City {
  id             String           @id @db.Uuid
  geoNameId      String?          @db.VarChar(15)
  name           String           @db.VarChar(50)
  countryIso2    String           @db.Char(2)
  latitude       Decimal?         @db.Decimal(8, 6)
  longitude      Decimal?         @db.Decimal(9, 6)
  mapboxId       String?          @db.VarChar(50)
  createdAt      DateTime         @default(now()) @db.Timestamptz()
  updatedAt      DateTime         @updatedAt @db.Timestamptz()
  Country        Country          @relation(fields: [countryIso2], references: [iso2], onDelete: Cascade)
  Port           Port[]
  PortRawData    PortRawData[]
  Announcement   Announcement[]
  AddressRawData AddressRawData[]

  @@index([name])
  @@schema("master")
}

model Country {
  iso2      String   @id @unique @db.Char(2)
  iso3      String   @unique @db.Char(3)
  name      String   @db.VarChar(50)
  native    String?  @db.VarChar(100)
  continent String?  @db.VarChar(50)
  capital   String?  @db.VarChar(100)
  createdAt DateTime @default(now()) @db.Timestamptz()
  updatedAt DateTime @updatedAt @db.Timestamptz()

  Identity       Identity[]
  Visa           Visa[]
  CallingCode    CallingCode?
  City           City[]
  Currency       Currency?
  Port           Port[]
  CityRawData    CityRawData[]
  PortRawData    PortRawData[]
  ShipRawData    ShipRawData[]
  Ship           Ship[]
  Profile        Profile[]
  Entity         Entity[]
  Timezone       Timezone[]
  Announcement   Announcement[]
  AddressRawData AddressRawData[]
  Job Job[]

  @@index([name])
  @@schema("master")
}

model CallingCode {
  countryIso2 String   @id @unique @db.Char(2)
  code        String   @db.VarChar(10)
  createdAt   DateTime @default(now()) @db.Timestamptz()
  updatedAt   DateTime @updatedAt @db.Timestamptz()
  Country     Country  @relation(fields: [countryIso2], references: [iso2], onDelete: Cascade)

  @@index([code])
  @@schema("master")
}

model Timezone {
  timezone    String   @db.VarChar(500)
  countryIso2 String   @db.VarChar(2)
  utcOffset   Int
  dstOffset   Int
  createdAt   DateTime @default(now()) @db.Timestamptz()
  updatedAt   DateTime @updatedAt @db.Timestamptz()

  Country     Country       @relation(fields: [countryIso2], references: [iso2], onDelete: Cascade)
  Port        Port[]
  PortRawData PortRawData[]

  @@id([timezone, countryIso2])
  @@index([timezone])
  @@schema("master")
}

model Currency {
  countryIso2 String   @id @unique @db.Char(2)
  code        String   @db.VarChar(5)
  name        String   @db.VarChar(50)
  symbol      String   @db.VarChar(50)
  numeric     Int
  decimal     Int
  createdAt   DateTime @default(now()) @db.Timestamptz()
  updatedAt   DateTime @updatedAt @db.Timestamptz()
  Country     Country  @relation(fields: [countryIso2], references: [iso2], onDelete: Cascade)

  @@index([code, name])
  @@schema("master")
}

model NewsProviderTopic {
  id         String   @default(uuid()) @db.Uuid
  cursorId   BigInt   @default(autoincrement()) @db.BigInt()
  providerId String   @db.Uuid
  topicId    String   @db.Uuid
  scrapeUrl  String   @db.VarChar(500)
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now()) @db.Timestamp()
  updatedAt  DateTime @updatedAt @db.Timestamp()

  NewsProvider NewsProvider @relation(fields: [providerId], references: [id], onDelete: Cascade)
  NewsTopic    NewsTopic    @relation(fields: [topicId], references: [id], onDelete: Cascade)

  @@id([id])
  @@unique([providerId, topicId])
  @@index([isActive])
  @@schema("master")
}

model NewsTopic {
  id          String   @default(uuid()) @db.Uuid
  cursorId    BigInt   @default(autoincrement()) @db.BigInt()
  name        String   @db.VarChar(100)
  slug        String   @db.VarChar(100)
  description String   @db.VarChar(500)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now()) @db.Timestamp()
  updatedAt   DateTime @updatedAt @db.Timestamp()

  NewsProviderTopic NewsProviderTopic[]
  NewsTopicMapping  NewsTopicMapping[]

  @@id([id])
  @@index([slug])
  @@index([isActive])
  @@schema("master")
}

model NewsProvider {
  id        String   @default(uuid()) @db.Uuid
  cursorId  BigInt   @default(autoincrement()) @db.BigInt()
  name      String   @db.VarChar(100)
  baseUrl   String   @db.VarChar(500)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now()) @db.Timestamp()
  updatedAt DateTime @updatedAt @db.Timestamp()

  News              News[]
  NewsProviderTopic NewsProviderTopic[]

  @@id([id])
  @@index([isActive])
  @@schema("master")
}

//#endregion master

// region news
model News {
  id            String   @default(uuid()) @db.Uuid
  cursorId      BigInt   @default(autoincrement()) @db.BigInt()
  title         String   @db.VarChar(500)
  link          String   @unique @db.VarChar(1000)
  publishedDate DateTime @db.Timestamp()
  providerId    String   @db.Uuid
  scrapedAt     DateTime @default(now()) @db.Timestamp()
  createdAt     DateTime @default(now()) @db.Timestamp()
  updatedAt     DateTime @updatedAt @db.Timestamp()

  NewsProvider     NewsProvider       @relation(fields: [providerId], references: [id])
  NewsTopicMapping NewsTopicMapping[]

  @@id([id])
  @@index([link])
  @@index([providerId, scrapedAt])
  @@index([cursorId])
  @@schema("news")
}

model NewsTopicMapping {
  id        String   @default(uuid()) @db.Uuid
  cursorId  BigInt   @default(autoincrement()) @db.BigInt()
  newsId    String   @db.Uuid
  topicId   String   @db.Uuid
  createdAt DateTime @default(now()) @db.Timestamp()

  News  News      @relation(fields: [newsId], references: [id], onDelete: Cascade)
  Topic NewsTopic @relation(fields: [topicId], references: [id], onDelete: Cascade)

  @@id([id])
  @@unique([newsId, topicId])
  @@index([newsId])
  @@index([topicId])
  @@schema("news")
}

// endregion news

//#region network
enum RequestStatusE {
  PENDING
  ACCEPTED
  REJECTED
  REVOKED
  DISCONNECTED
  BLOCKED

  @@schema("network")
}

enum FollowTypeE {
  USER
  ENTITY

  @@schema("network")
}

model Request {
  id                String         @id @default(uuid()) @db.Uuid
  senderProfileId   String         @db.Uuid
  receiverProfileId String         @db.Uuid
  count             Int            @default(1)
  status            RequestStatusE @default(PENDING)

  requestSentAt   DateTime @default(now()) @db.Timestamp()
  createdAt       DateTime @default(now()) @db.Timestamp()
  updatedAt       DateTime @updatedAt @db.Timestamp()
  ReceiverProfile Profile  @relation("ReceiverProfile", fields: [receiverProfileId], references: [id])
  SenderProfile   Profile  @relation("SenderProfile", fields: [senderProfileId], references: [id])

  @@unique([senderProfileId, receiverProfileId])
  @@schema("network")
}

model Connection {
  cursorId    BigInt   @default(autoincrement()) @db.BigInt()
  profileId   String   @db.Uuid
  connectedId String   @db.Uuid
  createdAt   DateTime @default(now()) @db.Timestamp()
  updatedAt   DateTime @updatedAt @db.Timestamp()
  ConnectedId Profile  @relation("ConnectedProfiles", fields: [connectedId], references: [id])
  Profile     Profile  @relation("ProfileConnections", fields: [profileId], references: [id])

  @@unique([profileId, connectedId])
  @@index([profileId, connectedId])
  @@schema("network")
}

model BlockedProfile {
  blockerId String   @db.Uuid
  blockedId String   @db.Uuid
  createdAt DateTime @default(now()) @db.Timestamp()
  updatedAt DateTime @updatedAt @db.Timestamp()
  Blocked   Profile  @relation("BlockedProfile", fields: [blockedId], references: [id])
  Blocker   Profile  @relation("BlockerProfile", fields: [blockerId], references: [id])

  @@unique([blockerId, blockedId])
  @@schema("network")
}

model Follow {
  id                      String         @id @default(uuid()) @db.Uuid
  cursorId                BigInt         @default(autoincrement()) @db.BigInt()
  followerProfileId       String?        @db.Uuid
  followeeProfileId       String?        @db.Uuid
  followerEntityProfileId String?        @db.Uuid
  followeeEntityProfileId String?        @db.Uuid
  createdAt               DateTime       @default(now()) @db.Timestamp()
  updatedAt               DateTime       @updatedAt @db.Timestamp()
  FollowerProfile         Profile?       @relation("FollowerProfile", fields: [followerProfileId], references: [id])
  FolloweeProfile         Profile?       @relation("FolloweeProfile", fields: [followeeProfileId], references: [id])
  FollowerEntityProfile   EntityProfile? @relation("FollowerEntityProfile", fields: [followerEntityProfileId], references: [id])
  FolloweeEntityProfile   EntityProfile? @relation("FolloweeEntityProfile", fields: [followeeEntityProfileId], references: [id])

  @@index([cursorId])
  @@schema("network")
}

//#endregion network
//#region port
enum PortStatusE {
  PENDING
  REJECTED
  APPROVED
  SAVED

  @@schema("port")
}

enum ScrapBookPostStatusE {
  ACTIVE
  DELETED
  FLAGGED

  @@schema("port")
}

enum ScrapBookReactionTypeE {
  LIKE
  SUPPORT
  CELEBRATE
  FUNNY

  @@schema("port")
}

model Port {
  unLocode      String   @id @db.VarChar(5)
  name          String   @db.VarChar(255)
  imageUrl      String?  @db.Text
  cityId        String   @db.Uuid
  countryIso2   String   @db.Char(2)
  timezone      String?  @db.VarChar(500)
  latitude      Decimal? @db.Decimal(10, 7)
  longitude     Decimal? @db.Decimal(11, 7)
  noOfTerminals Int?
  noOfBerths    Int?
  maxDraught    Decimal? @db.Decimal(10, 2)
  maxDeadweight Decimal? @db.Decimal(8, 2)
  maxLength     Decimal? @db.Decimal(7, 2)
  maxAirDraught Decimal? @db.Decimal(10, 2)

  createdAt DateTime @default(now()) @db.Timestamptz()
  updatedAt DateTime @updatedAt @db.Timestamptz()

  City    City    @relation(fields: [cityId], references: [id], onDelete: Cascade)
  Country Country @relation(fields: [countryIso2], references: [iso2], onDelete: Cascade)

  Timezone Timezone? @relation(fields: [timezone, countryIso2], references: [timezone, countryIso2], onDelete: Cascade)

  PortContribution      PortContribution[]
  PortImageContribution PortImageContribution[]
  PortVisitor           PortVisitor[]
  ScrapBookPost         ScrapBookPost[]
  CityRawData           CityRawData[]           @relation("CityRawDataToPort")
  Announcement          Announcement[]

  @@index([unLocode])
  @@index([name])
  @@schema("port")
}

model PortImageContribution {
  id                  String       @id @default(uuid()) @db.Uuid
  portUnLocode        String?      @db.VarChar(5)
  portRawDataUnLocode String?      @db.VarChar(5)
  imageUrl            String       @db.Text
  dataStatus          PortStatusE  @default(PENDING)
  profileId           String       @db.Uuid
  createdAt           DateTime     @default(now()) @db.Timestamptz()
  updatedAt           DateTime     @updatedAt @db.Timestamptz()
  PortRawData         PortRawData? @relation(fields: [portRawDataUnLocode], references: [unLocode])
  Port                Port?        @relation(fields: [portUnLocode], references: [unLocode])
  Profile             Profile      @relation(fields: [profileId], references: [id])

  @@schema("port")
}

model PortContribution {
  id                  String       @id @default(uuid()) @db.Uuid
  portUnLocode        String?      @db.VarChar(5)
  portRawDataUnLocode String?      @db.VarChar(5)
  profileId           String       @db.Uuid
  label               String       @db.VarChar(100)
  value               String       @db.VarChar(100)
  dataStatus          PortStatusE  @default(PENDING)
  createdAt           DateTime     @default(now()) @db.Timestamptz()
  updatedAt           DateTime     @updatedAt @db.Timestamptz()
  PortRawData         PortRawData? @relation(fields: [portRawDataUnLocode], references: [unLocode])
  Port                Port?        @relation(fields: [portUnLocode], references: [unLocode])
  Profile             Profile      @relation(fields: [profileId], references: [id])

  @@unique([profileId, portUnLocode, portRawDataUnLocode, label])
  @@schema("port")
}

model ScrapBookPost {
  id                String               @id @default(uuid()) @db.Uuid
  text              String               @db.VarChar(1000)
  textPreview       String               @db.VarChar(120)
  profileId         String               @db.Uuid
  status            ScrapBookPostStatusE @default(ACTIVE)
  reactionCount     Int                  @default(0)
  commentCount      Int                  @default(0)
  portUnLocode      String               @db.VarChar(5)
  createdAt         DateTime             @default(now()) @db.Timestamptz()
  updatedAt         DateTime             @updatedAt @db.Timestamptz()
  ScrapBookComment  ScrapBookComment[]
  Port              Port                 @relation(fields: [portUnLocode], references: [unLocode])
  Profile           Profile              @relation(fields: [profileId], references: [id])
  ScrapBookReaction ScrapBookReaction[]

  @@schema("port")
}

model ScrapBookReaction {
  profileId       String                 @db.Uuid
  scrapBookPostId String                 @db.Uuid
  reactionType    ScrapBookReactionTypeE @default(LIKE)
  createdAt       DateTime               @default(now()) @db.Timestamp()
  updatedAt       DateTime               @updatedAt @db.Timestamptz()
  Profile         Profile                @relation(fields: [profileId], references: [id], onDelete: Cascade)
  ScrapBookPost   ScrapBookPost          @relation(fields: [scrapBookPostId], references: [id], onDelete: Cascade)

  @@id([profileId, scrapBookPostId])
  @@schema("port")
}

model ScrapBookComment {
  id                      String                    @id @default(uuid()) @db.Uuid
  text                    String                    @db.VarChar(300)
  cursorId                BigInt                    @default(autoincrement()) @db.BigInt()
  scrapBookPostId         String                    @db.Uuid
  profileId               String                    @db.Uuid
  status                  PostStatusE               @default(ACTIVE)
  parentCommentId         String?                   @db.Uuid
  repliesCount            Int?
  createdAt               DateTime                  @default(now()) @db.Timestamptz()
  updatedAt               DateTime                  @updatedAt @db.Timestamptz()
  Parent                  ScrapBookComment?         @relation("Replies", fields: [parentCommentId], references: [id], onDelete: Cascade)
  Replies                 ScrapBookComment[]        @relation("Replies")
  Profile                 Profile                   @relation(fields: [profileId], references: [id])
  ScrapBookPost           ScrapBookPost             @relation(fields: [scrapBookPostId], references: [id], onDelete: Cascade)
  ScrapBookCommentMention ScrapBookCommentMention[]

  @@schema("port")
}

model ScrapBookCommentMention {
  id                 String           @id @default(uuid()) @db.Uuid
  commentId          String           @db.Uuid
  mentionedProfileId String           @db.Uuid
  createdAt          DateTime         @default(now()) @db.Timestamptz()
  updatedAt          DateTime         @updatedAt @db.Timestamptz()
  ScrapBookComment   ScrapBookComment @relation(fields: [commentId], references: [id])
  Profile            Profile          @relation(fields: [mentionedProfileId], references: [id])

  @@schema("port")
}

model PortVisitor {
  id                  String       @id @default(uuid()) @db.Uuid
  profileId           String       @db.Uuid
  portUnLocode        String?      @db.VarChar(5)
  portRawDataUnLocode String?      @db.VarChar(5)
  createdAt           DateTime     @default(now()) @db.Timestamptz()
  updatedAt           DateTime     @updatedAt @db.Timestamptz()
  PortRawData         PortRawData? @relation(fields: [portRawDataUnLocode], references: [unLocode])
  Port                Port?        @relation(fields: [portUnLocode], references: [unLocode])
  Profile             Profile      @relation(fields: [profileId], references: [id])

  @@schema("port")
}

//#endregion port

//#region rawData

enum StatusRawData {
  PENDING
  REJECTED
  APPROVED
  SAVED

  @@schema("rawData")
}

enum ServiceRawDataStatusE {
  ACTIVE
  DETAINED
  SCRAPPED

  @@schema("rawData")
}

enum ShipCapacityUnitTypeRawDataE {
  DEADWEIGHT_TONNAGE
  NOS
  LITRE
  LITRES_PER_HOUR
  CUBIC_METRE
  CUBIC_METRE_PER_HOUR
  TONNES
  TONNES_PER_HOUR
  TWENTY_FOOT_EQUIVALENT_UNIT

  @@schema("rawData")
}

enum EntityTypeRawDataE {
  COMPANY
  EDUCATION
  GO
  NGO
  IGO
  SMO
  OTHER

  @@schema("rawData")
}

enum SkillCategoryRawDataE {
  MARITIME
  OTHER

  @@schema("rawData")
}

enum CertificateCourseRawDataTypeE {
  STATUTORY
  VALUE_ADDED

  @@schema("rawData")
}

model DepartmentRawData {
  id             String           @id @default(uuid()) @db.Uuid
  name           String           @db.VarChar(100)
  dataStatus     StatusRawData    @default(PENDING)
  createdAt      DateTime         @default(now()) @db.Timestamp()
  updatedAt      DateTime         @updatedAt @db.Timestamp()
  ExperienceShip ExperienceShip[]
  Question       Question[]
  Job            Job[]

  @@schema("rawData")
}

model DesignationRawData {
  id                       String                     @id @default(uuid()) @db.Uuid
  name                     String                     @db.VarChar(100)
  dataStatus               StatusRawData              @default(PENDING)
  createdAt                DateTime                   @default(now()) @db.Timestamp()
  updatedAt                DateTime                   @updatedAt @db.Timestamp()
  ExperienceDesignation    ExperienceDesignation[]
  Profile                  Profile[]
  Job                      Job[]
  JobExperienceRequirement JobExperienceRequirement[]

  @@schema("rawData")
}

model ShipRawData {
  imo                     String                  @id @db.VarChar(7)
  name                    String?                 @db.VarChar(100)
  flagCountryIso2         String?                 @db.Char(2)
  callSign                String?                 @db.VarChar(7)
  mmsi                    Int?                    @db.Integer
  length                  Int?                    @db.Integer
  beam                    Int?                    @db.Integer
  yearBuilt               Int?                    @db.Integer
  gt                      Int?                    @db.Integer
  dwt                     Int?                    @db.Integer
  mainVesselTypeId        String?                 @db.Uuid
  mainVesselTypeRawDataId String?                 @db.Uuid
  subVesselTypeId         String?                 @db.Uuid
  subVesselTypeRawDataId  String?                 @db.Uuid
  dataStatus              StatusRawData           @default(PENDING)
  createdAt               DateTime                @default(now()) @db.Timestamptz()
  updatedAt               DateTime                @updatedAt @db.Timestamptz()
  ExperienceShip          ExperienceShip[]
  Country                 Country?                @relation(fields: [flagCountryIso2], references: [iso2])
  MainVesselType          MainVesselType?         @relation(fields: [mainVesselTypeId], references: [id])
  MainVesselTypeRawData   MainVesselTypeRawData?  @relation(fields: [mainVesselTypeRawDataId], references: [id])
  SubVesselType           SubVesselType?          @relation(fields: [subVesselTypeId], references: [id])
  SubVesselTypeRawData    SubVesselTypeRawData?   @relation(fields: [subVesselTypeRawDataId], references: [id])
  ShipContribution        ShipContribution[]
  ShipImageContribution   ShipImageContribution[]
  Question                Question[]
  Job                     Job[]

  @@schema("rawData")
}

enum PlaceSourceE {
  PROFILE
  MAPBOX

  @@schema("rawData")
}

model CityRawData {
  id             String           @id @default(uuid()) @db.Uuid
  geoNameId      String?          @db.VarChar(15)
  name           String           @db.VarChar(50)
  countryIso2    String           @db.Char(2)
  dataStatus     StatusRawData    @default(PENDING)
  latitude       Decimal?         @db.Decimal(6, 6)
  longitude      Decimal?         @db.Decimal(7, 6)
  mapboxId       String?          @db.VarChar(50)
  source         PlaceSourceE     @default(PROFILE)
  createdAt      DateTime         @default(now()) @db.Timestamptz()
  updatedAt      DateTime         @updatedAt @db.Timestamptz()
  Country        Country          @relation(fields: [countryIso2], references: [iso2], onDelete: Cascade)
  PortRawData    PortRawData[]
  Port           Port[]           @relation("CityRawDataToPort")
  Announcement   Announcement[]
  AddressRawData AddressRawData[]

  @@index([name])
  @@schema("rawData")
}

model AddressRawData {
  id            String   @default(uuid()) @db.Uuid
  text          String   @db.VarChar(1000)
  mapboxId      String   @db.VarChar(50)
  latitude      Decimal  @db.Decimal(8, 6)
  longitude     Decimal  @db.Decimal(9, 6)
  cityId        String?  @db.Uuid
  cityRawDataId String?  @db.Uuid
  countryIso2   String
  pincode       String   @db.VarChar(7)
  createdAt     DateTime @default(now()) @db.Timestamptz()
  updatedAt     DateTime @updatedAt @db.Timestamptz()

  City         City?          @relation(fields: [cityId], references: [id], onDelete: Cascade)
  CityRawData  CityRawData?   @relation(fields: [cityRawDataId], references: [id], onDelete: Cascade)
  Country      Country        @relation(fields: [countryIso2], references: [iso2], onDelete: Cascade)
  Announcement Announcement[]

  @@id([id])
  @@schema("rawData")
}

model PortRawData {
  unLocode              String                  @id @unique @db.VarChar(5)
  name                  String?                 @db.VarChar(100)
  cityId                String?                 @db.Uuid
  cityRawDataId         String?                 @db.Uuid
  countryIso2           String                  @db.Char(2)
  timezoneIso2          String?                 @db.Char(2)
  latitude              Decimal?                @db.Decimal(6, 6)
  longitude             Decimal?                @db.Decimal(7, 6)
  noOfTerminals         Int?
  noOfBerths            Int?
  maxDraught            Decimal?                @db.Decimal(10, 2)
  maxDeadweight         Decimal?                @db.Decimal(8, 2)
  maxLength             Decimal?                @db.Decimal(7, 2)
  maxAirDraught         Decimal?                @db.Decimal(10, 2)
  dataStatus            StatusRawData           @default(PENDING)
  createdAt             DateTime                @default(now()) @db.Timestamptz()
  updatedAt             DateTime                @updatedAt @db.Timestamptz()
  PortContribution      PortContribution[]
  PortImageContribution PortImageContribution[]
  PortVisitor           PortVisitor[]
  City                  City?                   @relation(fields: [cityId], references: [id], onDelete: Cascade)
  CityRawData           CityRawData?            @relation(fields: [cityRawDataId], references: [id], onDelete: Cascade)
  Country               Country                 @relation(fields: [countryIso2], references: [iso2], onDelete: Cascade)
  Timezone              Timezone?               @relation(fields: [timezoneIso2, countryIso2], references: [timezone, countryIso2], onDelete: Cascade)
  Announcement          Announcement[]

  @@schema("rawData")
}

model SubVesselTypeRawData {
  id               String   @id @default(uuid()) @db.Uuid
  mainVesselTypeId String?  @db.Uuid
  name             String   @db.VarChar(100)
  createdAt        DateTime @default(now()) @db.Timestamptz()
  updatedAt        DateTime @updatedAt @db.Timestamptz()

  mainVesselType MainVesselType?  @relation(fields: [mainVesselTypeId], references: [id])
  ExperienceShip ExperienceShip[]
  ShipRawData    ShipRawData[]
  Question       Question[]
  Job            Job[]
  JobExperienceRequirement JobExperienceRequirement[]

  @@schema("rawData")
}

model EntityRawData {
  id                            String                          @id @default(uuid()) @db.Uuid
  name                          String                          @db.VarChar(255)
  memberCount                   Int                             @default(0)
  website                       String?                         @db.VarChar(255)
  type                          EntityTypeE
  dataStatus                    StatusRawData                   @default(PENDING)
  createdAt                     DateTime                        @default(now()) @db.Timestamp()
  updatedAt                     DateTime                        @updatedAt @db.Timestamp()
  Experience                    Experience[]
  ProfileCertificate            ProfileCertificate[]
  ProfileEducation              ProfileEducation[]
  ProfileSkillEntityCertificate ProfileSkillEntityCertificate[]
  ProfileSkillEntityDegree      ProfileSkillEntityDegree[]
  Profile                       Profile[]
  EntityMember                  EntityMember[]
  EntityTab                     EntityTab[]
  EntityProfile                 EntityProfile[]
  Job                           Job[]
  EntityRequest                 EntityRequest[]
  JobApplication                JobApplication[]

  @@schema("rawData")
}

model SkillRawData {
  id                            String                          @id @default(uuid()) @db.Uuid
  name                          String                          @db.VarChar(100)
  category                      SkillCategoryRawDataE
  createdAt                     DateTime                        @default(now()) @db.Timestamp()
  updatedAt                     DateTime                        @updatedAt @db.Timestamp()
  ProfileSkill                  ProfileSkill[]
  ProfileSkillEntityCertificate ProfileSkillEntityCertificate[]
  ProfileSkillEntityDegree      ProfileSkillEntityDegree[]
  ProfileSkillExperienceShip    ProfileSkillExperienceShip[]
  JobSkillRequirement           JobSkillRequirement[]

  @@schema("rawData")
}

model DegreeRawData {
  id                       String                     @id @default(uuid()) @db.Uuid
  name                     String                     @db.VarChar(150)
  dataStatus               StatusRawData              @default(PENDING)
  createdAt                DateTime                   @default(now()) @db.Timestamptz()
  updatedAt                DateTime                   @updatedAt @db.Timestamptz()
  ProfileEducation         ProfileEducation[]
  ProfileSkillEntityDegree ProfileSkillEntityDegree[]

  @@schema("rawData")
}

model TopicRawData {
  id            String          @default(uuid()) @db.Uuid
  name          String          @db.VarChar(100)
  count         Int             @default(0)
  createdAt     DateTime        @default(now()) @db.Timestamptz()
  updatedAt     DateTime        @updatedAt @db.Timestamptz()
  QuestionTopic QuestionTopic[]

  @@id([id])
  @@schema("rawData")
}

model CertificateCourseRawData {
  id                          String                        @id @default(uuid()) @db.Uuid
  name                        String                        @db.VarChar(255)
  dataStatus                  StatusRawData                 @default(PENDING)
  type                        CertificateCourseTypeE
  createdAt                   DateTime                      @default(now()) @db.Timestamptz()
  updatedAt                   DateTime                      @updatedAt @db.Timestamptz()
  ProfileCertificate          ProfileCertificate[]
  JobCertificationRequirement JobCertificationRequirement[]

  @@schema("rawData")
}

model EntityBenefitRawData {
  id               String             @default(uuid()) @db.Uuid
  title            String             @db.VarChar(255)
  dataStatus       StatusRawData      @default(PENDING)
  cursorId         BigInt             @default(autoincrement()) @db.BigInt()
  createdAt        DateTime           @default(now()) @db.Timestamptz()
  updatedAt        DateTime           @updatedAt @db.Timestamptz()
  JobEntityBenefit JobEntityBenefit[]

  @@id([id])
  @@schema("rawData")
}

model FuelTypeRawData {
  id                 String               @id @default(uuid()) @db.Uuid
  name               String               @db.VarChar(100)
  dataStatus         StatusRawData        @default(PENDING)
  createdAt          DateTime             @default(now()) @db.Timestamptz()
  updatedAt          DateTime             @updatedAt @db.Timestamptz()
  ExperienceFuelType ExperienceFuelType[]
  JobEquipmentRequirement JobEquipmentRequirement[]

  @@schema("rawData")
}

model EquipmentCategoryRawData {
  id                          String                        @id @default(uuid()) @db.Uuid
  name                        String                        @db.VarChar(100)
  hasFuelType                 Boolean                       @default(false)
  dataStatus                  StatusRawData                 @default(PENDING)
  createdAt                   DateTime                      @default(now()) @db.Timestamptz()
  updatedAt                   DateTime                      @updatedAt @db.Timestamptz()
  ExperienceEquipmentCategory ExperienceEquipmentCategory[]
  Question                    Question[]
  JobEquipmentRequirement     JobEquipmentRequirement[]

  @@schema("rawData")
}

model EquipmentManufacturerRawData {
  id                              String                            @id @default(uuid()) @db.Uuid
  name                            String                            @db.VarChar(100)
  dataStatus                      StatusRawData                     @default(PENDING)
  createdAt                       DateTime                          @default(now()) @db.Timestamptz()
  updatedAt                       DateTime                          @updatedAt @db.Timestamptz()
  Question                        Question[]
  JobEquipmentRequirement         JobEquipmentRequirement[]
  ExperienceEquipmentManufacturer ExperienceEquipmentManufacturer[]

  @@schema("rawData")
}

model EquipmentModelRawData {
  id                       String                     @id @default(uuid()) @db.Uuid
  name                     String                     @db.VarChar(100)
  dataStatus               StatusRawData              @default(PENDING)
  createdAt                DateTime                   @default(now()) @db.Timestamptz()
  updatedAt                DateTime                   @updatedAt @db.Timestamptz()
  Question                 Question[]
  JobEquipmentRequirement  JobEquipmentRequirement[]
  ExperienceEquipmentModel ExperienceEquipmentModel[]

  @@schema("rawData")
}

model MainVesselTypeRawData {
  id                       String                     @id @default(uuid()) @db.Uuid
  name                     String                     @db.VarChar(100)
  createdAt                DateTime                   @default(now()) @db.Timestamptz()
  updatedAt                DateTime                   @updatedAt @db.Timestamptz()
  ShipRawData              ShipRawData[]
  Question                 Question[]

  @@schema("rawData")
}


model DocumentTypeRawData {
  id           String            @id @default(uuid()) @db.Uuid
  name         String            @db.VarChar(150)
  category     DocumentCategoryE
  dataStatus   StatusRawData    @default(PENDING)
  createdAt    DateTime          @default(now()) @db.Timestamp()
  updatedAt    DateTime          @updatedAt @db.Timestamp()

  Identity     Identity[]
  Visa         Visa[]
  JobDocumentRequirement  JobDocumentRequirement[]

  @@schema("rawData")
}

//#endregion rawData

//#region score
enum ScoreTypeE {
  CONTRIBUTION
  POST_CONTRIBUTION
  QNA_ANSWER
  TROUBLESHOOT_ANSWER
  INVITE_CONTRIBUTION
  QNA_ANSWER_VERIFIED
  QNA_ANSWER_LESS_THAN_TWO_HOURS
  QNA_ANSWER_TWO_TO_FOUR_HOURS
  QNA_ANSWER_FOUR_TO_SIX_HOURS
  QNA_ANSWER_SIX_TO_EIGHT_HOURS
  QNA_ANSWER_EIGHT_TO_TWENTYFOUR_HOURS
  TROUBLESHOOT_ANSWER_VERIFIED
  TROUBLESHOOT_ANSWER_LESS_THAN_TWO_HOURS
  TROUBLESHOOT_ANSWER_TWO_TO_FOUR_HOURS
  TROUBLESHOOT_ANSWER_FOUR_TO_SIX_HOURS
  TROUBLESHOOT_ANSWER_SIX_TO_EIGHT_HOURS
  TROUBLESHOOT_ANSWER_EIGHT_TO_TWENTYFOUR_HOURS

  @@schema("score")
}

model Reward {
  id             String           @default(uuid()) @db.Uuid
  type           ScoreTypeE
  score          Int
  createdAt      DateTime         @default(now()) @db.Timestamptz()
  updatedAt      DateTime         @updatedAt @db.Timestamptz()
  RewardAssigned RewardAssigned[]

  @@id([id])
  @@schema("score")
}

model RewardAssigned {
  id        String   @default(uuid()) @db.Uuid
  score     Int
  rewardId  String   @db.Uuid
  profileId String   @db.Uuid
  createdAt DateTime @default(now()) @db.Timestamptz()
  updatedAt DateTime @updatedAt @db.Timestamptz()

  Reward  Reward  @relation(fields: [rewardId], references: [id])
  Profile Profile @relation(fields: [profileId], references: [id])

  @@id([id])
  @@schema("score")
}

model RewardProfile {
  profileId               String   @db.Uuid
  contributionScore       Int      @default(0)
  qnaAnswerScore          Int      @default(0)
  troubleshootAnswerScore Int      @default(0)
  totalScore              Int      @default(0)
  createdAt               DateTime @default(now()) @db.Timestamptz()
  updatedAt               DateTime @updatedAt @db.Timestamptz()

  Profile Profile @relation(fields: [profileId], references: [id])

  @@id([profileId])
  @@schema("score")
}

//#endregion score

//#region leaderboard
model ContributionWeeklyLeaderboard {
  profileId String   @db.Uuid
  score     Int
  rank      Int
  createdAt DateTime @default(now()) @db.Timestamptz()
  updatedAt DateTime @updatedAt @db.Timestamptz()

  @@id([profileId])
  @@map("ContributionWeeklyLeaderboard")
  @@ignore
  @@schema("leaderboard")
}

model QnAAnswerWeeklyLeaderboard {
  profileId String   @db.Uuid
  score     Int
  rank      Int
  createdAt DateTime @default(now()) @db.Timestamptz()
  updatedAt DateTime @updatedAt @db.Timestamptz()

  @@id([profileId])
  @@map("QnAAnswerWeeklyLeaderboard")
  @@ignore
  @@schema("leaderboard")
}

model TroubleshootWeeklyLeaderboard {
  profileId String   @db.Uuid
  score     Int
  rank      Int
  createdAt DateTime @default(now()) @db.Timestamptz()
  updatedAt DateTime @updatedAt @db.Timestamptz()

  @@id([profileId])
  @@map("TroubleshootWeeklyLeaderboard")
  @@ignore
  @@schema("leaderboard")
}

model TotalWeeklyLeaderboard {
  profileId String   @db.Uuid
  score     Int
  rank      Int
  createdAt DateTime @default(now()) @db.Timestamptz()
  updatedAt DateTime @updatedAt @db.Timestamptz()

  @@id([profileId])
  @@map("TotalWeeklyLeaderboard")
  @@ignore
  @@schema("leaderboard")
}

model ContributionOverallLeaderboard {
  profileId String   @db.Uuid
  score     Int
  rank      Int
  createdAt DateTime @default(now()) @db.Timestamptz()
  updatedAt DateTime @updatedAt @db.Timestamptz()

  @@id([profileId])
  @@map("ContributionOverallLeaderboard")
  @@ignore
  @@schema("leaderboard")
}

model QnAAnswerOverallLeaderboard {
  profileId String   @db.Uuid
  score     Int
  rank      Int
  createdAt DateTime @default(now()) @db.Timestamptz()
  updatedAt DateTime @updatedAt @db.Timestamptz()

  @@id([profileId])
  @@map("QnAAnswerOverallLeaderboard")
  @@ignore
  @@schema("leaderboard")
}

model TroubleshootOverallLeaderboard {
  profileId String   @db.Uuid
  score     Int
  rank      Int
  createdAt DateTime @default(now()) @db.Timestamptz()
  updatedAt DateTime @updatedAt @db.Timestamptz()

  @@id([profileId])
  @@map("TroubleshootOverallLeaderboard")
  @@ignore
  @@schema("leaderboard")
}

model TotalOverallLeaderboard {
  profileId String   @db.Uuid
  score     Int
  rank      Int
  createdAt DateTime @default(now()) @db.Timestamptz()
  updatedAt DateTime @updatedAt @db.Timestamptz()

  @@id([profileId])
  @@map("TotalOverallLeaderboard")
  @@ignore
  @@schema("leaderboard")
}

//#endregion leaderboard

//#region ship

enum ServiceStatusE {
  ACTIVE
  DETAINED
  SCRAPPED

  @@schema("ship")
}

enum ShipStatusE {
  PENDING
  REJECTED
  APPROVED
  SAVED

  @@schema("ship")
}

enum ShipContributionLabelE {
  mmsi
  callSign
  name
  flagCountryIso2
  generalVesselType
  otherVesselType
  status
  portOfRegistry
  yearBuilt

  @@schema("ship")
}

model Ship {
  imo                   String                  @id @db.VarChar(7)
  name                  String                  @db.VarChar(100)
  flagCountryIso2       String?                 @db.Char(2)
  callSign              String?                 @db.VarChar(7)
  mmsi                  Int?                    @db.Integer
  length                Int?                    @db.Integer
  beam                  Int?                    @db.Integer
  yearBuilt             Int?                    @db.Integer
  gt                    Int?                    @db.Integer
  dwt                   Int?                    @db.Integer
  imageUrl              String?                 @db.Text
  mainVesselTypeId      String                  @db.Uuid
  subVesselTypeId       String?                 @db.Uuid
  status                ServiceStatusE?
  createdAt             DateTime                @default(now()) @db.Timestamptz()
  updatedAt             DateTime                @updatedAt @db.Timestamptz()
  ExperienceShip        ExperienceShip[]
  Country               Country?                @relation(fields: [flagCountryIso2], references: [iso2])
  MainVesselType        MainVesselType?         @relation(fields: [mainVesselTypeId], references: [id])
  SubVesselType         SubVesselType?          @relation(fields: [subVesselTypeId], references: [id])
  ShipContribution      ShipContribution[]
  ShipImageContribution ShipImageContribution[]
  ShipName              ShipName[]
  Question              Question[]
  Job                   Job[]

  @@schema("ship")
}

model ShipName {
  id        String    @id @default(uuid()) @db.Uuid
  name      String    @db.VarChar(100)
  shipImo   String    @db.VarChar(7)
  fromDate  DateTime? @db.Date
  toDate    DateTime? @db.Date
  createdAt DateTime  @default(now()) @db.Timestamptz()
  updatedAt DateTime  @updatedAt @db.Timestamptz()
  Ship      Ship      @relation(fields: [shipImo], references: [imo])

  @@schema("ship")
}

model ShipImageContribution {
  id             String       @id @default(uuid()) @db.Uuid
  shipImo        String?      @db.VarChar(7)
  shipRawDataImo String?      @db.VarChar(7)
  imageUrl       String       @db.Text
  dataStatus     ShipStatusE  @default(PENDING)
  profileId      String?      @db.Uuid
  createdAt      DateTime     @default(now()) @db.Timestamptz()
  updatedAt      DateTime     @updatedAt @db.Timestamptz()
  Profile        Profile?     @relation(fields: [profileId], references: [id])
  Ship           Ship?        @relation(fields: [shipImo], references: [imo])
  ShipRawData    ShipRawData? @relation(fields: [shipRawDataImo], references: [imo])

  @@schema("ship")
}

model ShipContribution {
  id             String                 @id @default(uuid()) @db.Uuid
  shipImo        String?                @db.VarChar(7)
  shipRawDataImo String?                @db.VarChar(7)
  profileId      String                 @db.Uuid
  label          ShipContributionLabelE
  value          String                 @db.VarChar(100)
  dataStatus     ShipStatusE            @default(PENDING)
  createdAt      DateTime               @default(now()) @db.Timestamptz()
  updatedAt      DateTime               @updatedAt @db.Timestamptz()
  Profile        Profile                @relation(fields: [profileId], references: [id])
  Ship           Ship?                  @relation(fields: [shipImo], references: [imo])
  ShipRawData    ShipRawData?           @relation(fields: [shipRawDataImo], references: [imo])

  @@unique([profileId, shipImo, shipRawDataImo, label])
  @@schema("ship")
}

model FuelType {
  id                 String               @id @default(uuid()) @db.Uuid
  name               String               @db.VarChar(100)
  createdAt          DateTime             @default(now()) @db.Timestamptz()
  updatedAt          DateTime             @updatedAt @db.Timestamptz()
  ExperienceFuelType ExperienceFuelType[]
  JobEquipmentRequirement JobEquipmentRequirement[]

  @@schema("ship")
}

model EquipmentManufacturer {
  id                              String                            @id @default(uuid()) @db.Uuid
  name                            String                            @db.VarChar(100)
  createdAt                       DateTime                          @default(now()) @db.Timestamptz()
  updatedAt                       DateTime                          @updatedAt @db.Timestamptz()
  Question                        Question[]
  JobEquipmentRequirement         JobEquipmentRequirement[]
  ExperienceEquipmentManufacturer ExperienceEquipmentManufacturer[]

  @@schema("ship")
}

model EquipmentModel {
  id                       String                     @id @default(uuid()) @db.Uuid
  name                     String                     @db.VarChar(100)
  createdAt                DateTime                   @default(now()) @db.Timestamptz()
  updatedAt                DateTime                   @updatedAt @db.Timestamptz()
  Question                 Question[]
  JobEquipmentRequirement  JobEquipmentRequirement[]
  ExperienceEquipmentModel ExperienceEquipmentModel[]

  @@schema("ship")
}

model EquipmentCategory {
  id                          String                        @id @default(uuid()) @db.Uuid
  name                        String                        @db.VarChar(100)
  hasFuelType                 Boolean                       @default(false)
  createdAt                   DateTime                      @default(now()) @db.Timestamptz()
  updatedAt                   DateTime                      @updatedAt @db.Timestamptz()
  ExperienceEquipmentCategory ExperienceEquipmentCategory[]
  Question                    Question[]
  JobEquipmentRequirement     JobEquipmentRequirement[]

  @@schema("ship")
}

model MainVesselType {
  id        String   @id @default(uuid()) @db.Uuid
  name      String   @db.VarChar(100)
  createdAt DateTime @default(now()) @db.Timestamptz()
  updatedAt DateTime @updatedAt @db.Timestamptz()

  SubVesselType SubVesselType[]

  SubVesselTypeRawData     SubVesselTypeRawData[]
  ShipRawData              ShipRawData[]
  Ship                     Ship[]
  Question                 Question[]

  @@schema("ship")
}

model SubVesselType {
  id               String   @id @default(uuid()) @db.Uuid
  mainVesselTypeId String   @db.Uuid
  name             String   @db.VarChar(100)
  createdAt        DateTime @default(now()) @db.Timestamptz()
  updatedAt        DateTime @updatedAt @db.Timestamptz()

  MainVesselType MainVesselType   @relation(fields: [mainVesselTypeId], references: [id])
  ExperienceShip ExperienceShip[]
  Ship           Ship[]
  ShipRawData    ShipRawData[]
  Question       Question[]
  Job            Job[]
  JobExperienceRequirement JobExperienceRequirement[]

  @@schema("ship")
}

enum ShipCapacityUnitTypeE {
  DEADWEIGHT_TONNAGE
  NOS
  LITRE
  LITRES_PER_HOUR
  CUBIC_METRE
  CUBIC_METRE_PER_HOUR
  TONNES
  TONNES_PER_HOUR
  TWENTY_FOOT_EQUIVALENT_UNIT

  @@schema("ship")
}

//#endregion ship
//#region user

enum GenderE {
  MALE
  FEMALE
  OTHER

  @@schema("user")
}

enum ProfileStatusE {
  ACTIVE
  INACTIVE
  SCHEDULED_FOR_DELETION
  BLOCKED
  DELETED

  @@schema("user")
}

model SavedPost {
  id        String   @id @default(uuid()) @db.Uuid
  profileId String   @db.Uuid
  postId    String   @db.Uuid
  cursorId  BigInt   @default(autoincrement()) @db.BigInt()
  createdAt DateTime @default(now()) @db.Timestamp()
  updatedAt DateTime @updatedAt @db.Timestamp()

  Profile Profile @relation(fields: [profileId], references: [id])
  Post    Post    @relation(fields: [postId], references: [id], onDelete: Cascade)

  @@unique([profileId, postId])
  @@schema("feed")
}

model Profile {
  id                              String                            @default(uuid()) @db.Uuid
  email                           String?                           @db.VarChar(255)
  password                        String?
  username                        String?                           @db.VarChar(25)
  googleSub                       String?                           @db.VarChar(255)
  appleSub                        String?                           @db.VarChar(255)
  name                            String?                           @db.VarChar(50)
  avatar                          String?                           @db.Text
  gender                          GenderE?
  status                          ProfileStatusE                    @default(ACTIVE)
  description                     String?                           @db.VarChar(255)
  countryIso2                     String?                           @db.Char(2)
  designationText                 String?                           @db.VarChar(100)
  entityText                      String?                           @db.VarChar(255)
  designationAlternativeId        String?                           @db.Uuid
  designationRawDataId            String?                           @db.Uuid
  entityId                        String?                           @db.Uuid
  entityRawDataId                 String?                           @db.Uuid
  followersCount                  Int                               @default(0)
  followingsCount                 Int                               @default(0)
  connectionsCount                Int                               @default(0)
  cursorId                        BigInt                            @default(autoincrement()) @db.BigInt()
  createdAt                       DateTime                          @default(now()) @db.Timestamp()
  updatedAt                       DateTime                          @updatedAt @db.Timestamp()
  Experience                      Experience[]
  ProfileCertificate              ProfileCertificate[]
  ProfileEducation                ProfileEducation[]
  Identity                        Identity[]
  Visa                            Visa[]
  Posts                           Post[]
  PostComments                    PostComment[]
  PostCommentReactions            PostCommentReaction[]
  PostMedia                       PostMedia[]
  PostReactions                   PostReaction[]
  BlockedByProfile                BlockedProfile[]                  @relation("BlockedProfile")
  BlockedProfile                  BlockedProfile[]                  @relation("BlockerProfile")
  ConnectedId                     Connection[]                      @relation("ConnectedProfiles")
  Connections                     Connection[]                      @relation("ProfileConnections")
  ReceivedRequests                Request[]                         @relation("ReceiverProfile")
  SentRequests                    Request[]                         @relation("SenderProfile")
  Follows                         Follow[]                          @relation("FollowerProfile")
  Following                       Follow[]                          @relation("FolloweeProfile")
  PortContribution                PortContribution[]
  PortImageContribution           PortImageContribution[]
  PortVisitor                     PortVisitor[]
  ScrapBookComment                ScrapBookComment[]
  ScrapBookCommentMention         ScrapBookCommentMention[]
  ScrapBookPost                   ScrapBookPost[]
  ScrapBookReaction               ScrapBookReaction[]
  ShipContribution                ShipContribution[]
  ShipImageContribution           ShipImageContribution[]
  Country                         Country?                          @relation(fields: [countryIso2], references: [iso2])
  DesignationAlternative          DesignationAlternative?           @relation(fields: [designationAlternativeId], references: [id])
  DesignationRawData              DesignationRawData?               @relation(fields: [designationRawDataId], references: [id])
  Entity                          Entity?                           @relation(fields: [entityId], references: [id])
  EntityRawData                   EntityRawData?                    @relation(fields: [entityRawDataId], references: [id])
  ProfileStatus                   ProfileStatus?
  ProfileMeta                     ProfileMeta?
  ProfileSkill                    ProfileSkill[]
  ProfileSkillEntityCertificate   ProfileSkillEntityCertificate[]
  ExperienceDesignation           ExperienceDesignation[]
  ExperienceShip                  ExperienceShip[]
  CommunityMember                 CommunityMember[]
  Community                       Community[]
  Question                        Question[]
  Answer                          Answer[]
  CommunityRequest                CommunityRequest[]
  QuestionComment                 QuestionComment[]
  QuestionVote                    QuestionVote[]
  AnswerVote                      AnswerVote[]
  AnswerComment                   AnswerComment[]
  ValidatedBy                     Answer[]                          @relation("ValidatedBy")
  RewardAssigned                  RewardAssigned[]
  RewardProfile                   RewardProfile[]
  RSVP                            RSVP[]
  Announcement                    Announcement[]
  EntityMember                    EntityMember[]
  EntityRequest                   EntityRequest[]
  ReferralCode                    ReferralCodes?
  ReferralStatusesAsReferred      ReferralStatus[]                  @relation("ReferredProfile")
  ReferralStatusesAsReferrer      ReferralStatus[]                  @relation("ReferrerProfile")
  Job                             Job[]
  ApplicantProfile                JobApplication[]                  @relation("ApplicantProfile")
  DecisionMakerProfile            JobApplication[]                  @relation("DecisionMakerProfile")
  ExperienceEquipmentCategory     ExperienceEquipmentCategory[]
  ExperienceEquipmentManufacturer ExperienceEquipmentManufacturer[]
  ExperienceEquipmentModel        ExperienceEquipmentModel[]
  EntityProfile                   EntityProfile[]
  SavedPosts                      SavedPost[]

  @@id([id])
  @@schema("user")
}

model ProfileStatus {
  profileId               String    @db.Uuid
  isPasswordSaved         Boolean   @default(false)
  isEmailVerified         Boolean   @default(false)
  isMobileVerified        Boolean   @default(false)
  isPersonalDetailsSaved  Boolean   @default(false)
  isWorkDetailsSaved      Boolean   @default(false)
  isPrivacyPolicyAccepted Boolean   @default(false)
  deletionInitiatedAt     DateTime?
  isAnonymous             Boolean   @default(false)
  createdAt               DateTime  @default(now()) @db.Timestamp()
  updatedAt               DateTime  @updatedAt @db.Timestamp()
  Profile                 Profile   @relation(fields: [profileId], references: [id])

  @@id(profileId)
  @@schema("user")
}

model ProfileMeta {
  profileId                       String    @db.Uuid
  receivedRequestCount            Int       @default(0)
  sentRequestCount                Int       @default(0)
  postCount                       Int       @default(0)
  educationCount                  Int       @default(0)
  statutoryCertCount              Int       @default(0)
  valueAddedCertCount             Int       @default(0)
  identityCount                   Int       @default(0)
  visaCount                       Int       @default(0)
  maritimeSkillsCount             Int       @default(0)
  otherSkillsCount                Int       @default(0)
  answerCount                     Int       @default(0)
  solutionCount                   Int       @default(0)
  normalQuestionScore             Int       @default(0)
  troubleshootQuestionScore       Int       @default(0)
  contributionScore               Int       @default(0)
  emailVerificationCount          Int       @default(0)
  emailVerificationFirstAttemptAt DateTime?
  passwordResetCount              Int       @default(0)
  passwordResetFirstAttemptAt     DateTime?

  createdAt DateTime @default(now()) @db.Timestamp()
  updatedAt DateTime @updatedAt @db.Timestamp()
  Profile   Profile  @relation(fields: [profileId], references: [id])

  @@id(profileId)
  @@schema("user")
}

//#endregion user

enum PostStatusE {
  ACTIVE
  DELETED
  FLAGGED

  @@schema("feed")
}

enum ReactionTypeE {
  LIKE
  SUPPORT
  CELEBRATE
  FUNNY

  @@schema("feed")
}

enum EntityTypeE {
  COMPANY
  EDUCATION
  GO
  NGO
  IGO
  SMO
  OTHER

  @@schema("company")
}

enum EntityMemberRoleE {
  ADMIN
  MAINTAINER
  MEMBER

  @@schema("company")
}

enum EntityRequestStatusE {
  PENDING
  APPROVED
  REJECTED
  REVOKED

  @@schema("company")
}

enum SkillCategoryE {
  MARITIME
  OTHER

  @@schema("company")
}

enum CertificateCourseTypeE {
  STATUTORY
  VALUE_ADDED

  @@schema("company")
}

enum EquipmentCategoryPowerCapacityUnitE {
  KILO_WATT
  LITRE
  LITRES_PER_HOUR
  CUBIC_METRE
  CUBIC_METRE_PER_HOUR
  TONNES
  TONNES_PER_HOUR

  @@schema("career")
}

enum IdentityTypeE {
  PASSPORT
  CDC
  SID

  @@schema("document")
}

//#region referral
model ReferralCodes {
  id        String    @id @default(uuid())
  code      String    @unique
  profileId String    @unique @db.Uuid
  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  Profile        Profile          @relation(fields: [profileId], references: [id])
  ReferralStatus ReferralStatus[]

  @@schema("referral")
}

model ReferralStatus {
  id                 String    @id @default(uuid())
  referredProfileId  String    @unique @db.Uuid
  referrerProfileId  String    @db.Uuid
  referralCodeId     String
  joinedAt           DateTime  @default(now())
  isReferrerRewarded Boolean   @default(false)
  isReferredRewarded Boolean   @default(false)
  createdAt          DateTime  @default(now())
  updatedAt          DateTime? @updatedAt

  ReferralCode    ReferralCodes @relation(fields: [referralCodeId], references: [id])
  ReferredProfile Profile       @relation("ReferredProfile", fields: [referredProfileId], references: [id])
  ReferrerProfile Profile       @relation("ReferrerProfile", fields: [referrerProfileId], references: [id])

  @@schema("referral")
}

//#endregion referral
