import { ProfileItemI } from '@/src/components/Announcement/types';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { IdNameTypeI, IdTypeI, ImoDataTypeI, ProfileBaseI } from '@/src/types/common/data';

export type fetchJobsForCandidateQueryI = {
  entity?: IdTypeI;
  isOfficial?: boolean;
  status?: string;
  pageSize: number;
  cursorId: string | null;
};

export type fetchJobsForCandidateBodyI = {
  designations?: IdTypeI[] | null;
  countries?: string[] | null;
  shipTypes?: IdTypeI[] | null;
  internetLimits?: Array<{ id: string; label: string }> | null;
};

export type fetchJobsForCandidateResultI = {
  data: {
    id: string;
    cursorId: string;
    isUrgent: boolean;
    entity: IdNameTypeI;
    designation: IdNameTypeI;
    department: IdNameTypeI;
    ship: Omit<SearchResultI, 'id'> & { imo: string };

    minYears: number;
    maxYears?: number | null;
    minSalary: number;
    maxSalary?: number | null;
    status?: string | null;
    expiryDate?: string;
    isOfficial: boolean;
    createdAt: string;
    applicationStatus?: string | null;
    creator: ProfileBaseI;

    matching?: number | null;
  }[];
  nextCursorId: null | string;
};

export type fetchJobsForApplicantQueryI = {
  cursorId: string | null;
  pageSize: number;
  status: string;
};

export type fetchJobsForApplicantBodyI = {
  designations?: IdTypeI[] | null;
  countries?: string[] | null;
  shipTypes?: IdTypeI[] | null;
  internetLimits?: Array<{ id: string; label: string }> | null;
};

export type fetchJobsForApplicantsResultI = {
  data: {
    id: string;
    cursorId: string | null;
    status: string;
    matching: number;
    designation: IdNameTypeI;
    entity: IdNameTypeI;
  }[];
  nextCursorId: null | string;
};

export type fetchApplicantsForJobPostsQueryI = {
  jobId: string;
  status: string;
  cursorId: string | null;
  pageSize: number;
};

export type fetchApplicantsForJobPostsBodyI = {
  designations: IdTypeI[];
  countries: string[];
  yearsOfExperiences: string[];
};

export type fetchApplicantsForJobPostsResultI = {
  data: {
    id: string;
    cursorId: string | null;
    matching: number;
    status: string;
    ApplicantProfile: ProfileItemI;
    DecisionMakerProfile: ProfileItemI;
    createdAt: string;
  }[];
  nextCursorId: string | null;
};

export type FiltersForJobsResultI = {};

export type fetchFiltersForEntityMemberForApplicantsQueryI = {
  jobId: string;
  status: string;
};

export type CreateJobStageOneBodyI = {
  entity: IdTypeI,
  designation: IdTypeI,
  ship?: ImoDataTypeI
  shipType?: IdTypeI,
  showShipDetails?: boolean,
  applicationMethod: string,
  applicationEmail?: string,
  applicationUrl?: string,
  jobType: string
  expiryDate: string
  joiningDate?:string;
  genderDiversityIndex: number,
  isOfficial:boolean;
  countryIso2:string
}

export type CreateJobStageOneResultI = {
 id:string
}

export type CreateJobStageTwoBodyI = {
  jobId:string,
  about?: string,
  rolesResponsibilities?: string
  requirements?: string
  benefits?: string
}

export type CreateJobStageTwoResultI = {

}