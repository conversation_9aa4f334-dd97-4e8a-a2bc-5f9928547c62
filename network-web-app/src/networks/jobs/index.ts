import { apiCall } from '@/lib/api';
import {
  FetchJobsQueryI,
  FetchJobsResponseI,
  FetchJobsForApplicantQueryI,
  FetchJobsForApplicantResponseI,
} from './types';

export const fetchJobsForCandidateAPI = async (
  query: FetchJobsQueryI,
  payload?: Partial<FetchJobsForCandidateBodyI>
): Promise<FetchJobsResponseI> => {
  const defaultPayload: FetchJobsForCandidateBodyI = {
    designations: null,
    countries: [],
    shipTypes: null,
    internetLimits: [],
  };

  const result = await apiCall<
    FetchJobsQueryI | FetchJobsForCandidateBodyI,
    FetchJobsResponseI
  >('/backend/api/v1/company/job/candidate', 'POST', {
    isAuth: true,
    query,
    payload: { ...defaultPayload, ...payload },
  });
  return result;
};

export const fetchJobsForEntityMemberAPI = async (
  query: FetchJobsQueryI,
  payload: FetchJobsForEntityMemberPayloadI
): Promise<FetchJobsResponseI> => {
  const result = await apiCall<
    FetchJobsQueryI | FetchJobsForEntityMemberPayloadI,
    FetchJobsResponseI
  >('/backend/api/v1/company/job/entity-member', 'POST', {
    isAuth: true,
    query,
    payload,
  });
  return result;
};

import type {
  JobCandidateFetchOneResultI,
  FetchApplicantsQueryI,
  FetchApplicantsResponseI,
  FetchJobsForApplicantBodyI,
  FetchJobsForCandidateBodyI,
  FetchJobsForEntityMemberPayloadI,
  JobFiltersI,
  FetchFiltersQueryI,
  FetchFiltersForEntityMemberQueryI,
} from './types';

export const fetchJobDetailAPI = async (
  jobId: string
): Promise<JobCandidateFetchOneResultI> => {
  const result = await apiCall<string, JobCandidateFetchOneResultI>(
    `/backend/api/v1/company/job/candidate/${jobId}`,
    'GET',
    { isAuth: true }
  );
  return result;
};

export const closeJobAPI = async (jobId: string): Promise<void> => {
  await apiCall<unknown, unknown>(
    `/backend/api/v1/company/job/core/${jobId}`,
    'PATCH',
    {
      isAuth: true,
      payload: { expiryDate: new Date().toISOString() },
    }
  );
};

export const fetchApplicantsForJobAPI = async (
  query: FetchApplicantsQueryI
): Promise<FetchApplicantsResponseI> => {
  const result = await apiCall<FetchApplicantsQueryI, FetchApplicantsResponseI>(
    '/backend/api/v1/company/job/application/entity-member',
    'GET',
    {
      isAuth: true,
      query,
    }
  );
  return result;
};

export const fetchJobsForApplicantAPI = async (
  query: FetchJobsForApplicantQueryI,
  payload?: Partial<FetchJobsForApplicantBodyI>
): Promise<FetchJobsForApplicantResponseI> => {
  const defaultPayload: FetchJobsForApplicantBodyI = {
    countries: [],
    designations: null,
    shipTypes: null,
    internetLimits: [],
  };

  const result = await apiCall<
    FetchJobsForApplicantQueryI | FetchJobsForApplicantBodyI,
    FetchJobsForApplicantResponseI
  >('/backend/api/v1/company/jobs/application/applicant', 'POST', {
    isAuth: true,
    query,
    payload: { ...defaultPayload, ...payload },
  });
  return result;
};

export const fetchFiltersForEntityMemberAPI = async (
  query: FetchFiltersForEntityMemberQueryI
): Promise<JobFiltersI> => {
  const result = await apiCall<FetchFiltersForEntityMemberQueryI, JobFiltersI>(
    '/backend/api/v1/company/jobs/entity-member/filters',
    'GET',
    {
      isAuth: true,
      query,
    }
  );
  return result;
};

// Filter APIs
export const fetchFiltersForCandidateAPI = async (): Promise<JobFiltersI> => {
  const result = await apiCall<unknown, JobFiltersI>(
    '/backend/api/v1/company/jobs/candidate/filters',
    'GET',
    {
      isAuth: true,
      query: {
        isOfficial: false,
      },
    }
  );
  return result;
};

export const fetchFiltersForApplicantAPI = async (
  query: FetchFiltersQueryI
): Promise<JobFiltersI> => {
  const result = await apiCall<unknown, JobFiltersI>(
    '/backend/api/v1/company/job/applicant/filters',
    'GET',
    {
      isAuth: true,
      query,
    }
  );
  return result;
};
