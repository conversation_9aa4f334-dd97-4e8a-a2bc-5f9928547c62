import { apiCall } from '@/src/services/api';
import { FiltersForJobsResultI } from './types';

export const fetchFiltersForEntityMembersAPI = async ({status}:{status:string}): Promise<FiltersForJobsResultI> => {
  const result = await apiCall<unknown, FiltersForJobsResultI[]>(
    '/backend/api/v1/company/jobs/entity-member/filters',
    'POST',
    {
      isAuth: true,
      query:{
        status
      }
    },
  );

  return result;
};
