import z from 'zod';
import { EntityProfile, JobApplication } from '@prisma/postgres';
import { ApplicationE } from '@consts/company/job/application';
import AppError from '@classes/AppError';
import { UUIDSchema } from '@navicater/b2c-internal-communication';
import { CountryIso2Schema, CursorPaginationSchema, IdLabelSchema, IdTypeSchema } from '@schemas/common/common';
import { ProfileTransformI } from '@interfaces/user/profile';
import { IdNameTypeI } from '@interfaces/common/data';

export namespace Application {
  export type Application = JobApplication;
  export const UpsertOneForApplicantResponseSchema = z.union([
    z.object({
      id: UUIDSchema
    }),
    z.object({
      redirectTo: z.literal('email'),
      email: z.string().email()
    }),
    z.object({
      redirectTo: z.literal('external'),
      url: z.string().url()
    })
  ]);

  export type UpsertOneForApplicantResponseI = z.infer<typeof UpsertOneForApplicantResponseSchema>;
  export const UpsertOneForApplicantSchema = z
    .object({
      applicationId: UUIDSchema.optional(),
      jobId: UUIDSchema,
      status: z.enum([
        ApplicationE.Status.Enum.PENDING,
        ApplicationE.Status.Enum.WITHDREW,
        ApplicationE.Status.Enum.ACCEPTED_BY_APPLICANT,
        ApplicationE.Status.Enum.REJECTED_BY_APPLICANT,
      ]),
    })
    .superRefine((data, _ctx) => {
      if (data.status !== 'PENDING' && !data.applicationId) {
        throw new AppError('APPL002');
      }
    });

  export type UpsertOneForApplicantI = z.infer<typeof UpsertOneForApplicantSchema>;

  export const UpdateOneForEntityMemberSchema = z.object({
    applicationId: UUIDSchema,
    status: z.enum([
      ApplicationE.Status.Enum.SHORTLISTED,
      ApplicationE.Status.Enum.REJECTED_BY_ENTITY,
      ApplicationE.Status.Enum.OFFERED,
    ]),
  });

  export type UpdateOneForEntityMemberI = z.infer<typeof UpdateOneForEntityMemberSchema>;

  export const FetchManyForApplicantQuerySchema = CursorPaginationSchema.extend({
    status: ApplicationE.Status.optional(),
  });
  export type FetchManyForApplicantQueryI = z.infer<typeof FetchManyForApplicantQuerySchema>;

  export const FetchManyForApplicantBodySchema = z.object({
    countries: z.array(CountryIso2Schema).optional(),
    designations: z.array(IdTypeSchema).optional().nullable(),
    shipTypes: z.array(IdTypeSchema).optional().nullable(),
    internetLimits: z.array(IdLabelSchema).optional()
  });
  export type FetchManyForApplicantBodyI = z.infer<typeof FetchManyForApplicantBodySchema>;

  export type FetchManyForApplicantResultI = Pick<Application, 'id' | 'cursorId' | 'matching' | 'status' | 'jobId'> & {
    designation: IdNameTypeI;
    entity: IdNameTypeI;
    entityProfile:Pick<EntityProfile, 'id' | 'name' | 'avatar' >
  };

  export const FetchManyForEntityMemberQuerySchema = CursorPaginationSchema.extend({
    jobId: UUIDSchema,
    status: ApplicationE.Status.optional(),
  });
  export type FetchManyForEntityMemberQueryI = z.infer<typeof FetchManyForEntityMemberQuerySchema>;

  export const FetchManyForEntityMemberBodySchema = z.object({
    designations: z.array(IdTypeSchema).optional().nullable(),
    countries: z.array(CountryIso2Schema).optional().nullable(),
    yearsOfExperiences: z.array(IdLabelSchema).optional().nullable(),
  });
  export type FetchManyForEntityMemberBodyI = z.infer<typeof FetchManyForEntityMemberBodySchema>;

  export const FetchFiltersForEntityMemberQuerySchema = z.object({
    jobId: UUIDSchema,
    status: ApplicationE.Status.optional(),
  });
  export type FetchFiltersForEntityMemberQueryI = z.infer<typeof FetchFiltersForEntityMemberQuerySchema>;

  export type FetchManyForEntityMemberResultI = Pick<
    Application,
    'id' | 'cursorId' | 'matching' | 'status' | 'createdAt'
  > & {
    ApplicantProfile: ProfileTransformI;
    DecisionMakerProfile: ProfileTransformI;
  };

  export const FetchFiltersForApplicantSchema = z.object({
    status: ApplicationE.Status.optional(),
  });
  export type FetchFiltersForApplicantI = z.infer<typeof FetchFiltersForApplicantSchema>;
}
