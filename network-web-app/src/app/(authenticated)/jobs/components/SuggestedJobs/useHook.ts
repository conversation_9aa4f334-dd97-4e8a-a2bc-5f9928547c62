'use client';

import { useState, useCallback, useMemo } from 'react';
import { FilterItemI, FilterTabI, JobFiltersI } from '@/networks/jobs/types';
import {
  fetchFiltersForCandidateAPI,
  fetchFiltersForApplicantAPI,
} from '@/networks/jobs';
import { UseJobFiltersProps, UseJobFiltersReturn } from './types';

const useJobFilters = ({
  activeTab,
}: UseJobFiltersProps): UseJobFiltersReturn => {
  const [filterData, setFilterData] = useState<Record<string, FilterItemI[]>>(
    {}
  );
  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, FilterItemI[]>
  >({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [isApplyingFilters, setIsApplyingFilters] = useState(false);

  const filterTabs: FilterTabI[] = useMemo(
    () => [
      {
        id: 'locations',
        label: 'Location',
        appliedCount: selectedFilters.locations?.length || 0,
      },
      {
        id: 'designations',
        label: 'Designation',
        appliedCount: selectedFilters.designations?.length || 0,
      },
      {
        id: 'shipTypes',
        label: 'Ship Type',
        appliedCount: selectedFilters.shipTypes?.length || 0,
      },
      {
        id: 'internetLimits',
        label: 'Internet',
        appliedCount: selectedFilters.internetLimits?.length || 0,
      },
    ],
    [selectedFilters]
  );

  const hasActiveFilters = useMemo(() => {
    return Object.values(selectedFilters).some(filters => filters.length > 0);
  }, [selectedFilters]);

  const activeFilterCount = useMemo(() => {
    return Object.values(selectedFilters).reduce(
      (total, filters) => total + filters.length,
      0
    );
  }, [selectedFilters]);

  const loadFilters = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      let filtersResponse: JobFiltersI;

      if (activeTab === 'SUGGUESTED') {
        filtersResponse = await fetchFiltersForCandidateAPI();
      } else {
        filtersResponse = await fetchFiltersForApplicantAPI({
          status: activeTab,
        });
      }

      setFilterData({
        locations: filtersResponse.locations || [],
        designations: filtersResponse.designations || [],
        shipTypes: filtersResponse.shipTypes || [],
        internetLimits: filtersResponse.internetLimits || [],
      });
    } catch (err) {
      console.error('Failed to load filters:', err);
      setError('Failed to load filters. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [activeTab]);

  const openFilterModal = useCallback(() => {
    setIsFilterModalOpen(true);
    if (Object.keys(filterData).length === 0) {
      loadFilters();
    }
  }, [filterData, loadFilters]);

  const closeFilterModal = useCallback(() => {
    setIsFilterModalOpen(false);
  }, []);

  const applyFilters = useCallback(
    (filters: Record<string, FilterItemI[]>, shouldRefetch = true) => {
      if (shouldRefetch) {
        setIsApplyingFilters(true);
        // Reset loading state after a delay
        setTimeout(() => setIsApplyingFilters(false), 1000);
      }
      setSelectedFilters(filters);
      setIsFilterModalOpen(false);
      return shouldRefetch;
    },
    []
  );

  const clearAllFilters = useCallback((shouldRefetch = true) => {
    if (shouldRefetch) {
      setIsApplyingFilters(true);
      // Reset loading state after a delay
      setTimeout(() => setIsApplyingFilters(false), 1000);
    }
    setSelectedFilters({});
    return shouldRefetch;
  }, []);

  const getFilteredPayload = useCallback(() => {
    const payload: any = {
      countries:
        selectedFilters.locations?.map(f => ({
          id: f.id,
          dataType: f.dataType || 'master',
        })) || [],
      designations:
        selectedFilters.designations?.length > 0
          ? selectedFilters.designations.map(f => ({
              id: f.id,
              dataType: f.dataType || 'master',
            }))
          : null,
      shipTypes:
        selectedFilters.shipTypes?.length > 0
          ? selectedFilters.shipTypes.map(f => ({
              id: f.id,
              dataType: f.dataType || 'master',
            }))
          : null,
      internetLimits:
        selectedFilters.internetLimits?.map(f => ({
          id: f.id,
          dataType: f.dataType || 'master',
        })) || [],
    };

    return payload;
  }, [selectedFilters]);

  return {
    filterTabs,
    filterData,
    selectedFilters,
    loading,
    error,
    isFilterModalOpen,
    hasActiveFilters,
    activeFilterCount,
    isApplyingFilters,
    openFilterModal,
    closeFilterModal,
    applyFilters,
    clearAllFilters,
    loadFilters,
    getFilteredPayload,
  };
};
export default useJobFilters;
