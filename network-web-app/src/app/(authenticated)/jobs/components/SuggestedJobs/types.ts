import {
  ApplicantsStatusI,
  FilterItemI,
  FilterTabI,
} from '@/networks/jobs/types';

export type SuggestedJobsPropsI = Record<string, never>;
export type SuggestedJobsTabI = {
  id: ApplicantsStatusI;
  label: string;
};

export type SuggestedJobItemI = {
  id: string;
  slug: string;
  title: string;
  company: string;
  postedAt: string;
  avatarUrl: string | null;
  matching?: number | null;
};

export type UseJobFiltersProps = {
  activeTab: ApplicantsStatusI;
};

export type UseJobFiltersReturn = {
  filterTabs: FilterTabI[];
  filterData: Record<string, FilterItemI[]>;
  selectedFilters: Record<string, FilterItemI[]>;
  loading: boolean;
  error: string | null;
  isFilterModalOpen: boolean;
  hasActiveFilters: boolean;
  activeFilterCount: number;
  isApplyingFilters: boolean;
  openFilterModal: () => void;
  closeFilterModal: () => void;
  applyFilters: (
    filters: Record<string, FilterItemI[]>,
    shouldRefetch?: boolean
  ) => boolean;
  clearAllFilters: (shouldRefetch?: boolean) => boolean;
  loadFilters: () => Promise<void>;
  getFilteredPayload: () => any;
};
