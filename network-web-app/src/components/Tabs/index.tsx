'use client';

import React from 'react';

export type TabItemI = {
  id: string;
  label: string;
  badge?: number;
};

export type TabsPropsI = {
  items: TabItemI[];
  activeId: string;
  onChange: (id: string) => void;
  className?: string;
};

const Tabs: React.FC<TabsPropsI> = ({
  items,
  activeId,
  onChange,
  className,
}) => {
  return (
    <div className={`border-b border-gray-200 relative ${className || ''}`}>
      <nav className="flex overflow-x-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none] scrollbar-hide touch-pan-x px-2 sm:px-0">
        {items.map((tab, index) => {
          const isActive = tab.id === activeId;
          return (
            <button
              key={tab.id}
              type="button"
              onClick={() => onChange(tab.id)}
              className={`flex-shrink-0 pb-3 px-2 sm:px-1 text-sm font-medium transition-colors relative outline-none focus:outline-none focus-visible:outline-none focus:ring-0 focus-visible:ring-0 whitespace-nowrap ${
                index > 0 ? 'ml-4 sm:ml-6' : ''
              } ${
                isActive
                  ? 'text-[#448600]'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              aria-current={isActive ? 'page' : undefined}
            >
              {tab.label}
              {tab.badge && tab.badge > 0 && (
                <span className="ml-2 inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-bold leading-none text-white bg-[#448600] rounded-full">
                  {tab.badge}
                </span>
              )}
              {isActive && (
                <span className="absolute -bottom-px left-0 right-0 h-0.5 bg-[#448600] rounded-t-sm" />
              )}
            </button>
          );
        })}
      </nav>

      {/* Fade indicators for scrollable content on mobile */}
      <div className="absolute left-0 top-0 bottom-0 w-4 bg-gradient-to-r from-white to-transparent pointer-events-none sm:hidden" />
      <div className="absolute right-0 top-0 bottom-0 w-4 bg-gradient-to-l from-white to-transparent pointer-events-none sm:hidden" />
    </div>
  );
};

export default Tabs;
